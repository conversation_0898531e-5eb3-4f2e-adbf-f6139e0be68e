import { NextResponse } from 'next/server';
import { ensureSettingsTable } from '@/lib/db-init';

// 全局变量，用于跟踪数据库是否已初始化
let dbInitialized = false;

/**
 * 初始化数据库API
 * 这个API会在应用启动时被调用，确保数据库表结构正确
 */
export async function GET() {
  try {
    // 只在首次调用时初始化数据库
    if (!dbInitialized) {
      await ensureSettingsTable();
      dbInitialized = true;
      console.log('数据库初始化成功');
    }
    
    return NextResponse.json({
      success: true,
      message: '数据库初始化成功'
    });
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库初始化失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
