import { NextResponse } from 'next/server';
import { run, query } from '@/lib/db';

/**
 * POST 请求处理函数 - 更新库存记录表结构以支持码上放心平台
 */
export async function POST() {
  try {
    console.log('开始更新库存记录表结构...');

    // 检查库存记录表是否存在
    const tableExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='库存记录'"
    );

    if (!tableExists || tableExists.length === 0) {
      // 创建库存记录表
      await run(`
        CREATE TABLE 库存记录 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          药品编号 INTEGER NOT NULL,
          操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '盘点', '调整')),
          数量变化 INTEGER NOT NULL,
          操作前库存 INTEGER NOT NULL DEFAULT 0,
          操作后库存 INTEGER NOT NULL DEFAULT 0,
          供应商编号 INTEGER,
          批次号 TEXT,
          有效期 DATE,
          成本价 DECIMAL(10,2),
          操作人 TEXT,
          操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          备注 TEXT,
          码上放心单据号 TEXT,
          码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')),
          码上放心上传时间 DATETIME,
          码上放心响应 TEXT,
          FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
          FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
        )
      `);
      console.log('库存记录表创建成功');
    } else {
      // 检查是否需要添加新字段
      const tableInfo = await query("PRAGMA table_info(库存记录)");
      const existingColumns = tableInfo.map((col: any) => col.name);
      
      console.log('现有字段:', existingColumns);

      // 定义需要添加的新字段
      const newColumns = [
        {
          name: '码上放心单据号',
          definition: 'TEXT',
          description: '码上放心平台单据编号'
        },
        {
          name: '码上放心上传状态',
          definition: 'TEXT CHECK (码上放心上传状态 IN (\'pending\', \'success\', \'failed\'))',
          description: '码上放心平台上传状态'
        },
        {
          name: '码上放心上传时间',
          definition: 'DATETIME',
          description: '码上放心平台上传时间'
        },
        {
          name: '码上放心响应',
          definition: 'TEXT',
          description: '码上放心平台响应数据'
        },
        {
          name: '批次号',
          definition: 'TEXT',
          description: '药品批次号'
        },
        {
          name: '有效期',
          definition: 'DATE',
          description: '药品有效期'
        },
        {
          name: '成本价',
          definition: 'DECIMAL(10,2)',
          description: '进货成本价'
        },
        {
          name: '供应商编号',
          definition: 'INTEGER',
          description: '供应商编号'
        }
      ];

      // 添加缺失的字段
      const addedColumns = [];
      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            await run(`ALTER TABLE 库存记录 ADD COLUMN ${column.name} ${column.definition}`);
            addedColumns.push(column.name);
            console.log(`成功添加字段: ${column.name}`);
          } catch (error) {
            console.error(`添加字段 ${column.name} 失败:`, error);
          }
        }
      }

      if (addedColumns.length > 0) {
        console.log(`成功添加 ${addedColumns.length} 个新字段:`, addedColumns);
      } else {
        console.log('所有必要字段已存在，无需添加');
      }
    }

    // 创建药品追溯码记录表（用于记录每次出入库的追溯码）
    const traceTableExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='药品追溯码记录'"
    );

    if (!traceTableExists || traceTableExists.length === 0) {
      await run(`
        CREATE TABLE 药品追溯码记录 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          库存记录编号 INTEGER NOT NULL,
          药品编号 INTEGER NOT NULL,
          追溯码 TEXT NOT NULL,
          操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库')),
          操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')) DEFAULT 'pending',
          FOREIGN KEY (库存记录编号) REFERENCES 库存记录(编号),
          FOREIGN KEY (药品编号) REFERENCES 药品信息(编号)
        )
      `);
      console.log('药品追溯码记录表创建成功');

      // 创建索引
      await run('CREATE INDEX idx_药品追溯码记录_追溯码 ON 药品追溯码记录(追溯码)');
      await run('CREATE INDEX idx_药品追溯码记录_库存记录编号 ON 药品追溯码记录(库存记录编号)');
      console.log('药品追溯码记录表索引创建成功');
    }

    // 获取更新后的表结构
    const updatedTableInfo = await query("PRAGMA table_info(库存记录)");
    const traceTableInfo = await query("PRAGMA table_info(药品追溯码记录)");
    
    return NextResponse.json({
      success: true,
      message: '库存记录表结构更新成功',
      data: {
        inventory_table: {
          columns: updatedTableInfo.length,
          structure: updatedTableInfo
        },
        trace_table: {
          columns: traceTableInfo.length,
          structure: traceTableInfo
        }
      }
    });

  } catch (error) {
    console.error('更新库存记录表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '更新库存记录表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
