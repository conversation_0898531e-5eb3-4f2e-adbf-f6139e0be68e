import { NextResponse } from 'next/server';
import { run, query } from '@/lib/db';

/**
 * POST 请求处理函数 - 优化供应商表结构
 */
export async function POST() {
  try {
    console.log('开始优化供应商表结构...');

    // 检查是否需要添加新字段
    const tableInfo = await query("PRAGMA table_info(供应商)");
    const existingColumns = tableInfo.map((col: any) => col.name);
    
    console.log('现有字段:', existingColumns);

    // 定义需要添加的新字段
    const newColumns = [
      {
        name: '营业执照号',
        definition: 'TEXT',
        description: '营业执照注册号'
      },
      {
        name: '执照有效期',
        definition: 'DATE',
        description: '营业执照有效期'
      },
      {
        name: 'GSP证书号',
        definition: 'TEXT',
        description: '药品经营质量管理规范证书号'
      },
      {
        name: 'GSP有效期',
        definition: 'DATE',
        description: 'GSP证书有效期'
      },
      {
        name: '经营范围',
        definition: 'TEXT',
        description: '经营范围描述'
      },
      {
        name: '质量负责人',
        definition: 'TEXT',
        description: '质量负责人姓名'
      },
      {
        name: '合作状态',
        definition: 'TEXT DEFAULT "active"',
        description: '合作状态：active-正常合作, suspended-暂停合作, terminated-终止合作'
      },
      {
        name: '备注',
        definition: 'TEXT',
        description: '备注信息'
      }
    ];

    // 添加缺失的字段
    const addedColumns = [];
    for (const column of newColumns) {
      if (!existingColumns.includes(column.name)) {
        try {
          await run(`ALTER TABLE 供应商 ADD COLUMN ${column.name} ${column.definition}`);
          addedColumns.push(column.name);
          console.log(`成功添加字段: ${column.name}`);
        } catch (error) {
          console.error(`添加字段 ${column.name} 失败:`, error);
        }
      }
    }

    // 检查是否需要在药品信息表中添加供应商关联字段
    const productTableInfo = await query("PRAGMA table_info(药品信息)");
    const productColumns = productTableInfo.map((col: any) => col.name);
    
    if (!productColumns.includes('供应商编号')) {
      try {
        await run('ALTER TABLE 药品信息 ADD COLUMN 供应商编号 INTEGER');
        console.log('成功在药品信息表中添加供应商编号字段');
      } catch (error) {
        console.error('添加供应商编号字段失败:', error);
      }
    }

    // 获取优化后的表结构
    const updatedTableInfo = await query("PRAGMA table_info(供应商)");
    
    return NextResponse.json({
      success: true,
      message: '供应商表结构优化成功',
      data: {
        addedColumns,
        totalColumns: updatedTableInfo.length,
        tableStructure: updatedTableInfo
      }
    });

  } catch (error) {
    console.error('优化供应商表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '优化供应商表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
