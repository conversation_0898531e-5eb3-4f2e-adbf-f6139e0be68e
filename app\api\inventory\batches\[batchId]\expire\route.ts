import { NextRequest, NextResponse } from 'next/server';
import { getDbConnection, run, query } from '@/lib/db';

interface RouteParams {
  params: {
    batchId: string;
  };
}

export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { batchId } = params;
    
    if (!batchId) {
      return NextResponse.json({ 
        success: false, 
        message: '批次ID不能为空' 
      }, { status: 400 });
    }

    // 获取数据库连接
    const db = await getDbConnection();
    
    try {
      // 开始事务
      await run('BEGIN TRANSACTION');
      
      // 1. 查找批次
      const batch = await query(`
        SELECT * FROM batch WHERE id = ?
      `, [batchId]);
      
      if (!batch || batch.length === 0) {
        return NextResponse.json({ 
          success: false, 
          message: '未找到指定批次' 
        }, { status: 404 });
      }
      
      const currentBatch = batch[0];
      
      // 2. 更新批次状态为过期
      await run(`
        UPDATE batch 
        SET status = 'expired' 
        WHERE id = ?
      `, [batchId]);
      
      // 3. 如果批次有剩余数量，需要减少产品库存
      if (currentBatch.quantity > 0 && currentBatch.status === 'active') {
        await run(`
          UPDATE product 
          SET stock_quantity = stock_quantity - ? 
          WHERE id = ?
        `, [currentBatch.quantity, currentBatch.product_id]);
        
        // 4. 创建出库记录
        await run(`
          INSERT INTO stock_out 
          (product_id, quantity, reason, stock_out_date, notes) 
          VALUES (?, ?, ?, ?, ?)
        `, [
          currentBatch.product_id, 
          currentBatch.quantity, 
          'expired', 
          new Date().toISOString(), 
          `批次 ${currentBatch.batch_number} 已标记为过期/作废`
        ]);
      }
      
      // 提交事务
      await run('COMMIT');
      
      return NextResponse.json({ 
        success: true, 
        data: {
          message: '批次已成功标记为过期/作废'
        }
      });
    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('标记批次为过期时出错:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理批次过期操作时出错' 
    }, { status: 500 });
  }
} 