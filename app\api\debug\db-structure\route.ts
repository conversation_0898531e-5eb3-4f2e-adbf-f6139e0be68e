import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * GET 请求处理函数 - 检查数据库表结构
 */
export async function GET() {
  try {
    // 获取所有表名
    const tables = await query(
      "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
    );
    
    const result: any = {
      tables: [],
      table_structures: {}
    };
    
    // 获取每个表的结构
    for (const table of tables) {
      const tableName = table.name;
      result.tables.push(tableName);
      
      try {
        // 获取表结构
        const structure = await query(`PRAGMA table_info(${tableName})`);
        result.table_structures[tableName] = structure;
      } catch (error) {
        result.table_structures[tableName] = { error: error.message };
      }
    }
    
    // 特别检查库存记录表
    try {
      const inventoryTableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='库存记录'"
      );
      result.inventory_table_exists = inventoryTableExists.length > 0;
      
      if (inventoryTableExists.length > 0) {
        const inventoryStructure = await query("PRAGMA table_info(库存记录)");
        result.inventory_table_structure = inventoryStructure;
        
        // 检查是否有操作类型字段
        const hasOperationType = inventoryStructure.some((col: any) => col.name === '操作类型');
        result.has_operation_type_field = hasOperationType;
      }
    } catch (error) {
      result.inventory_check_error = error.message;
    }
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('检查数据库结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '检查数据库结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
