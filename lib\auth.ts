/**
 * 权限控制和认证相关工具函数
 */

// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',           // 管理员
  PHARMACIST = 'pharmacist', // 药师
  CASHIER = 'cashier',       // 收银员
  VIEWER = 'viewer'          // 查看者
}

// 权限枚举
export enum Permission {
  // 订单权限
  VIEW_ORDERS = 'view_orders',
  CREATE_ORDERS = 'create_orders',
  EDIT_ORDERS = 'edit_orders',
  DELETE_ORDERS = 'delete_orders',
  
  // 库存权限
  VIEW_INVENTORY = 'view_inventory',
  MANAGE_INVENTORY = 'manage_inventory',
  
  // 药品权限
  VIEW_PRODUCTS = 'view_products',
  MANAGE_PRODUCTS = 'manage_products',
  
  // 统计权限
  VIEW_STATISTICS = 'view_statistics',
  
  // 系统权限
  MANAGE_SYSTEM = 'manage_system',
  VIEW_AUDIT_LOGS = 'view_audit_logs'
}

// 角色权限映射
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    Permission.VIEW_ORDERS,
    Permission.CREATE_ORDERS,
    Permission.EDIT_ORDERS,
    Permission.DELETE_ORDERS,
    Permission.VIEW_INVENTORY,
    Permission.MANAGE_INVENTORY,
    Permission.VIEW_PRODUCTS,
    Permission.MANAGE_PRODUCTS,
    Permission.VIEW_STATISTICS,
    Permission.MANAGE_SYSTEM,
    Permission.VIEW_AUDIT_LOGS
  ],
  [UserRole.PHARMACIST]: [
    Permission.VIEW_ORDERS,
    Permission.CREATE_ORDERS,
    Permission.EDIT_ORDERS,
    Permission.VIEW_INVENTORY,
    Permission.MANAGE_INVENTORY,
    Permission.VIEW_PRODUCTS,
    Permission.MANAGE_PRODUCTS,
    Permission.VIEW_STATISTICS
  ],
  [UserRole.CASHIER]: [
    Permission.VIEW_ORDERS,
    Permission.CREATE_ORDERS,
    Permission.VIEW_INVENTORY,
    Permission.VIEW_PRODUCTS
  ],
  [UserRole.VIEWER]: [
    Permission.VIEW_ORDERS,
    Permission.VIEW_INVENTORY,
    Permission.VIEW_PRODUCTS,
    Permission.VIEW_STATISTICS
  ]
};

// 用户信息接口
export interface User {
  id: number;
  username: string;
  name: string;
  role: UserRole;
  permissions: Permission[];
}

// 模拟当前用户（在实际应用中应该从session或token中获取）
export function getCurrentUser(): User {
  return {
    id: 1,
    username: 'admin',
    name: '系统管理员',
    role: UserRole.ADMIN,
    permissions: ROLE_PERMISSIONS[UserRole.ADMIN]
  };
}

// 检查用户是否有指定权限
export function hasPermission(user: User, permission: Permission): boolean {
  return user.permissions.includes(permission);
}

// 检查用户是否有任一权限
export function hasAnyPermission(user: User, permissions: Permission[]): boolean {
  return permissions.some(permission => user.permissions.includes(permission));
}

// 检查用户是否有所有权限
export function hasAllPermissions(user: User, permissions: Permission[]): boolean {
  return permissions.every(permission => user.permissions.includes(permission));
}

// 权限检查装饰器（用于API路由）
export function requirePermission(permission: Permission) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args: any[]) {
      const user = getCurrentUser();
      
      if (!hasPermission(user, permission)) {
        throw new Error(`权限不足：需要 ${permission} 权限`);
      }
      
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

// 记录操作日志的简化版本
export async function logOperation(
  action: string,
  target: string,
  details: string,
  result: 'success' | 'error' = 'success',
  user?: User
) {
  const currentUser = user || getCurrentUser();
  
  const logEntry = {
    timestamp: new Date().toISOString(),
    userId: currentUser.id,
    userName: currentUser.name,
    action,
    target,
    details,
    result,
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'Server',
    ipAddress: '127.0.0.1' // 在实际应用中应该获取真实IP
  };
  
  // 在实际应用中，这里应该写入数据库
  console.log('操作日志:', logEntry);
  
  // 可以选择发送到日志服务
  try {
    if (typeof window === 'undefined') {
      // 服务端环境，可以直接写入文件或数据库
      // 这里暂时只打印到控制台
    }
  } catch (error) {
    console.error('记录操作日志失败:', error);
  }
}

// 数据访问控制
export function filterOrdersByPermission(orders: any[], user: User): any[] {
  // 根据用户权限过滤订单数据
  if (hasPermission(user, Permission.VIEW_ORDERS)) {
    return orders;
  }
  
  // 如果没有查看订单权限，返回空数组
  return [];
}

// 敏感数据脱敏
export function sanitizeOrderData(order: any, user: User): any {
  const sanitized = { ...order };
  
  // 根据用户权限决定是否显示敏感信息
  if (!hasPermission(user, Permission.VIEW_STATISTICS)) {
    // 移除金额信息
    delete sanitized.totalAmount;
    delete sanitized.discountAmount;
    delete sanitized.payableAmount;
    delete sanitized.receivedAmount;
  }
  
  if (!hasPermission(user, Permission.MANAGE_INVENTORY)) {
    // 移除成本价信息
    delete sanitized.costPrice;
  }
  
  return sanitized;
}

// 操作权限验证
export function canPerformAction(action: string, user: User): boolean {
  switch (action) {
    case 'create_order':
      return hasPermission(user, Permission.CREATE_ORDERS);
    case 'edit_order':
      return hasPermission(user, Permission.EDIT_ORDERS);
    case 'delete_order':
      return hasPermission(user, Permission.DELETE_ORDERS);
    case 'view_statistics':
      return hasPermission(user, Permission.VIEW_STATISTICS);
    case 'manage_inventory':
      return hasPermission(user, Permission.MANAGE_INVENTORY);
    case 'manage_system':
      return hasPermission(user, Permission.MANAGE_SYSTEM);
    default:
      return false;
  }
}

// 生成权限错误消息
export function getPermissionErrorMessage(permission: Permission): string {
  const messages: Record<Permission, string> = {
    [Permission.VIEW_ORDERS]: '您没有查看订单的权限',
    [Permission.CREATE_ORDERS]: '您没有创建订单的权限',
    [Permission.EDIT_ORDERS]: '您没有编辑订单的权限',
    [Permission.DELETE_ORDERS]: '您没有删除订单的权限',
    [Permission.VIEW_INVENTORY]: '您没有查看库存的权限',
    [Permission.MANAGE_INVENTORY]: '您没有管理库存的权限',
    [Permission.VIEW_PRODUCTS]: '您没有查看药品的权限',
    [Permission.MANAGE_PRODUCTS]: '您没有管理药品的权限',
    [Permission.VIEW_STATISTICS]: '您没有查看统计的权限',
    [Permission.MANAGE_SYSTEM]: '您没有系统管理的权限',
    [Permission.VIEW_AUDIT_LOGS]: '您没有查看审计日志的权限'
  };
  
  return messages[permission] || '权限不足';
}
