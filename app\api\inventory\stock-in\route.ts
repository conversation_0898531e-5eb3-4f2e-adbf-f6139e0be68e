import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

interface StockInRequest {
  product_id: number;
  quantity: number;
  batch_number?: string;
  auto_generate_batch?: boolean; // 是否自动生成批次号
  batch_type?: string; // 批次类型，用于自动生成
  supplier_id?: number;
  cost_price?: number;
  expiry_date?: string;
  stock_in_date: string;
  notes?: string;
  trace_codes?: string[];
  upload_to_mashangfangxin?: boolean;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as StockInRequest;
    const {
      product_id,
      quantity,
      batch_number,
      auto_generate_batch,
      batch_type,
      supplier_id,
      cost_price,
      expiry_date,
      stock_in_date,
      notes,
      trace_codes,
      upload_to_mashangfangxin
    } = body;

    // 验证数据
    if (!product_id || !quantity || !stock_in_date) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：product_id, quantity, stock_in_date'
      }, { status: 400 });
    }

    if (quantity <= 0) {
      return NextResponse.json({
        success: false,
        message: '入库数量必须大于0'
      }, { status: 400 });
    }

    // 验证日期格式
    if (expiry_date && isNaN(Date.parse(expiry_date))) {
      return NextResponse.json({
        success: false,
        message: '有效期格式不正确'
      }, { status: 400 });
    }

    if (isNaN(Date.parse(stock_in_date))) {
      return NextResponse.json({
        success: false,
        message: '入库日期格式不正确'
      }, { status: 400 });
    }

    // 处理批次号
    let finalBatchNumber = batch_number;
    let batchNumberSource = 'manual'; // 'manual', 'auto', 'trace_code'

    // 如果需要自动生成批次号
    if (auto_generate_batch && !batch_number) {
      try {
        const batchTypeToUse = batch_type || 'PC'; // 默认为采购批次
        const batchResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/inventory/batch-number?type=${batchTypeToUse}&product_id=${product_id}`);

        if (batchResponse.ok) {
          const batchData = await batchResponse.json();
          if (batchData.success) {
            finalBatchNumber = batchData.data.batchNumber;
            batchNumberSource = 'auto';
          }
        }
      } catch (error) {
        console.error('自动生成批次号失败:', error);
        // 如果自动生成失败，使用简单的时间戳批次号
        const timestamp = Date.now();
        finalBatchNumber = `AUTO-${timestamp}`;
        batchNumberSource = 'auto';
      }
    }

    // 验证批次号格式
    if (finalBatchNumber) {
      if (finalBatchNumber.length > 50) {
        return NextResponse.json({
          success: false,
          message: '批次号长度不能超过50个字符'
        }, { status: 400 });
      }

      if (!/^[A-Za-z0-9\-_\u4e00-\u9fa5]+$/.test(finalBatchNumber)) {
        return NextResponse.json({
          success: false,
          message: '批次号只能包含字母、数字、连字符、下划线和中文字符'
        }, { status: 400 });
      }
    }

    // 1. 获取药品信息
    const productResult = await query(
      'SELECT 编号, 名称, 库存数量 FROM 药品信息 WHERE 编号 = ?',
      [product_id]
    );

    if (!productResult || productResult.length === 0) {
      return NextResponse.json({
        success: false,
        message: '未找到指定的药品'
      }, { status: 404 });
    }

    const product = productResult[0];
    const currentStock = product.库存数量 || 0;
    const newStock = currentStock + quantity;

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 2. 创建库存记录
    const stockInResult = await run(
      `INSERT INTO 库存记录 (
        药品编号, 操作类型, 数量变化, 操作前库存, 操作后库存,
        供应商编号, 批次号, 有效期, 成本价, 操作人, 备注, 操作时间
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        product_id,
        '入库',
        quantity,
        currentStock,
        newStock,
        supplier_id || null,
        finalBatchNumber || null,
        expiry_date || null,
        cost_price || null,
        '系统操作员', // 操作人，实际应用中应该从认证信息获取
        notes || null,
        stock_in_date
      ]
    );

    if (!stockInResult.lastID) {
      return NextResponse.json({
        success: false,
        message: '创建库存记录失败'
      }, { status: 500 });
    }

    const inventoryId = stockInResult.lastID;

    // 3. 更新药品库存
    await run(
      'UPDATE 药品信息 SET 库存数量 = ? WHERE 编号 = ?',
      [newStock, product_id]
    );

      // 4. 如果有追溯码，保存追溯码记录
      if (trace_codes && trace_codes.length > 0) {
        for (const traceCode of trace_codes) {
          if (traceCode.trim()) {
            await run(
              `INSERT INTO 药品追溯码记录 (
                库存记录编号, 药品编号, 追溯码, 操作类型
              ) VALUES (?, ?, ?, ?)`,
              [inventoryId, product_id, traceCode.trim(), '入库']
            );
          }
        }
      }

      // 提交事务
      await run('COMMIT');

      return NextResponse.json({
        success: true,
        message: '入库成功',
        data: {
          inventory_id: inventoryId,
          product_id: product_id,
          product_name: product.名称,
          quantity: quantity,
          batch_number: finalBatchNumber,
          batch_number_source: batchNumberSource,
          previous_stock: currentStock,
          new_stock: newStock,
          trace_codes_count: trace_codes ? trace_codes.length : 0,
          upload_to_mashangfangxin: upload_to_mashangfangxin || false
        }
      });

    } catch (transactionError) {
      // 回滚事务
      await run('ROLLBACK');
      console.error('入库事务处理错误:', transactionError);
      throw transactionError;
    }

  } catch (error) {
    console.error('入库操作失败:', error);
    return NextResponse.json({
      success: false,
      message: '入库操作失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}