# 订单管理系统技术维护指南

## 目录
1. [系统架构](#系统架构)
2. [API文档](#api文档)
3. [数据库设计](#数据库设计)
4. [部署指南](#部署指南)
5. [维护指南](#维护指南)
6. [故障排除](#故障排除)

## 系统架构

### 技术栈
- **前端框架：** Next.js 14 + React 18
- **样式框架：** Tailwind CSS
- **数据库：** SQLite3
- **运行环境：** Node.js 18+
- **包管理：** npm/yarn

### 目录结构
```
app/
├── orders/                 # 订单管理模块
│   ├── page.tsx            # 订单列表页面
│   ├── [id]/               # 订单详情页面
│   ├── statistics/         # 统计分析页面
│   ├── security/           # 权限管理页面
│   └── test/               # 功能测试页面
├── api/
│   └── orders/             # 订单相关API
│       ├── unified-simple/ # 统一查询API
│       ├── search-simple/  # 搜索API
│       ├── statistics-simple/ # 统计API
│       └── [id]/           # 详情API
lib/
├── db.ts                   # 数据库连接
├── auth.ts                 # 权限控制
└── db-init.ts              # 数据库初始化
```

### 组件架构
```
OrderManagement
├── OrderList              # 订单列表组件
├── OrderFilters           # 筛选组件
├── OrderSearch            # 搜索组件
├── OrderDetail            # 详情组件
├── OrderStatistics        # 统计组件
└── SecurityManagement     # 权限管理组件
```

## API文档

### 1. 统一订单查询API

**接口地址：** `GET /api/orders/unified-simple`

**请求参数：**
```typescript
interface QueryParams {
  page?: number;          // 页码，默认1
  limit?: number;         // 每页数量，默认20
  orderType?: string;     // 订单类型，逗号分隔
  status?: string;        // 订单状态，逗号分隔
  startDate?: string;     // 开始日期 YYYY-MM-DD
  endDate?: string;       // 结束日期 YYYY-MM-DD
  quickFilter?: string;   // 快捷筛选 today|week|month
}
```

**响应格式：**
```typescript
interface Response {
  success: boolean;
  data: {
    orders: Order[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
```

### 2. 订单搜索API

**接口地址：** `GET /api/orders/search-simple`

**请求参数：**
```typescript
interface SearchParams {
  keyword: string;        // 搜索关键词
  type?: string;          // 搜索类型 all|orderNumber|productName|barcode|batchNumber
  page?: number;          // 页码
  limit?: number;         // 每页数量
}
```

### 3. 订单统计API

**接口地址：** `GET /api/orders/statistics-simple`

**请求参数：**
```typescript
interface StatisticsParams {
  period?: string;        // 统计周期 today|week|month|quarter|year
  startDate?: string;     // 自定义开始日期
  endDate?: string;       // 自定义结束日期
}
```

### 4. 订单详情API

**接口地址：** `GET /api/orders/unified/[id]`

**请求参数：**
```typescript
interface DetailParams {
  type: string;           // 订单类型 sales|stock_in|stock_out|inventory|adjustment
}
```

## 数据库设计

### 主要数据表

#### 1. 销售订单表
```sql
CREATE TABLE 销售订单 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  订单编号 TEXT UNIQUE NOT NULL,
  客户编号 INTEGER,
  状态 TEXT DEFAULT '待处理',
  总金额 DECIMAL(10,2) DEFAULT 0,
  创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  备注 TEXT
);
```

#### 2. 库存记录表
```sql
CREATE TABLE 库存记录 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  药品编号 INTEGER NOT NULL,
  操作类型 TEXT NOT NULL,
  数量变化 INTEGER NOT NULL,
  操作前库存 INTEGER DEFAULT 0,
  操作后库存 INTEGER DEFAULT 0,
  批次号 TEXT,
  有效期 DATE,
  成本价 DECIMAL(10,2),
  操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  备注 TEXT
);
```

#### 3. 操作日志表
```sql
CREATE TABLE 操作日志 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  用户编号 INTEGER NOT NULL,
  用户名 TEXT NOT NULL,
  操作类型 TEXT NOT NULL,
  操作对象 TEXT NOT NULL,
  操作详情 TEXT,
  IP地址 TEXT,
  操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  操作结果 TEXT DEFAULT 'success'
);
```

### 索引优化
```sql
-- 订单查询优化
CREATE INDEX idx_sales_order_date ON 销售订单(创建时间);
CREATE INDEX idx_sales_order_status ON 销售订单(状态);
CREATE INDEX idx_inventory_date ON 库存记录(操作时间);
CREATE INDEX idx_inventory_type ON 库存记录(操作类型);

-- 搜索优化
CREATE INDEX idx_sales_order_number ON 销售订单(订单编号);
CREATE INDEX idx_inventory_batch ON 库存记录(批次号);

-- 日志查询优化
CREATE INDEX idx_audit_log_time ON 操作日志(操作时间);
CREATE INDEX idx_audit_log_user ON 操作日志(用户编号);
```

## 部署指南

### 环境要求
- **Node.js：** 18.0.0 或更高版本
- **内存：** 最少 2GB RAM
- **存储：** 最少 10GB 可用空间
- **网络：** 稳定的网络连接

### 部署步骤

#### 1. 代码部署
```bash
# 克隆代码
git clone <repository-url>
cd retail_trade_system

# 安装依赖
npm install

# 构建项目
npm run build

# 启动服务
npm start
```

#### 2. 数据库初始化
```bash
# 访问初始化接口
curl http://localhost:3001/api/init-db
```

#### 3. 环境配置
```bash
# 创建环境变量文件
cp .env.example .env.local

# 编辑配置
DATABASE_URL=./db/药店管理系统.db
NODE_ENV=production
PORT=3001
```

#### 4. 进程管理（推荐使用PM2）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "pharmacy-system" -- start

# 设置开机自启
pm2 startup
pm2 save
```

### 反向代理配置（Nginx）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 维护指南

### 日常维护

#### 1. 数据库维护
```sql
-- 清理过期日志（保留3个月）
DELETE FROM 操作日志 WHERE 操作时间 < DATE('now', '-3 months');

-- 重建索引
REINDEX;

-- 数据库优化
VACUUM;

-- 分析统计信息
ANALYZE;
```

#### 2. 性能监控
```bash
# 检查内存使用
free -h

# 检查磁盘空间
df -h

# 检查进程状态
pm2 status

# 查看应用日志
pm2 logs pharmacy-system
```

#### 3. 备份策略
```bash
# 数据库备份
cp ./db/药店管理系统.db ./backup/pharmacy_$(date +%Y%m%d_%H%M%S).db

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/path/to/backup"
DB_FILE="./db/药店管理系统.db"
DATE=$(date +%Y%m%d_%H%M%S)

cp $DB_FILE $BACKUP_DIR/pharmacy_$DATE.db

# 保留最近30天的备份
find $BACKUP_DIR -name "pharmacy_*.db" -mtime +30 -delete
```

### 更新部署

#### 1. 代码更新
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install

# 重新构建
npm run build

# 重启服务
pm2 restart pharmacy-system
```

#### 2. 数据库迁移
```bash
# 备份当前数据库
cp ./db/药店管理系统.db ./backup/before_migration_$(date +%Y%m%d).db

# 运行迁移脚本
npm run migrate

# 验证迁移结果
npm run verify-migration
```

## 故障排除

### 常见问题

#### 1. 应用无法启动
**症状：** 服务启动失败或端口占用

**排查步骤：**
```bash
# 检查端口占用
netstat -tulpn | grep :3001

# 检查进程状态
pm2 status

# 查看错误日志
pm2 logs pharmacy-system --err

# 检查依赖
npm audit
```

**解决方案：**
- 杀死占用端口的进程
- 检查环境变量配置
- 重新安装依赖包

#### 2. 数据库连接失败
**症状：** API返回数据库错误

**排查步骤：**
```bash
# 检查数据库文件
ls -la ./db/

# 检查文件权限
chmod 644 ./db/药店管理系统.db

# 测试数据库连接
sqlite3 ./db/药店管理系统.db ".tables"
```

**解决方案：**
- 确保数据库文件存在且可读写
- 重新初始化数据库
- 检查磁盘空间

#### 3. 性能问题
**症状：** 页面加载缓慢或超时

**排查步骤：**
```bash
# 检查系统资源
top
htop

# 检查数据库大小
du -h ./db/

# 分析慢查询
# 在代码中添加查询时间日志
```

**解决方案：**
- 优化数据库查询
- 添加适当的索引
- 增加服务器资源
- 实施查询缓存

#### 4. 权限问题
**症状：** 用户无法访问某些功能

**排查步骤：**
- 检查用户角色配置
- 验证权限控制逻辑
- 查看操作日志

**解决方案：**
- 更新用户权限
- 修复权限控制代码
- 重新分配角色

### 监控和告警

#### 1. 应用监控
```javascript
// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});
```

#### 2. 日志监控
```bash
# 监控错误日志
tail -f /var/log/pharmacy-system/error.log

# 设置日志轮转
logrotate /etc/logrotate.d/pharmacy-system
```

#### 3. 性能监控
- 使用 PM2 监控面板
- 集成 New Relic 或类似工具
- 设置关键指标告警

### 安全维护

#### 1. 定期安全检查
```bash
# 检查依赖漏洞
npm audit

# 更新安全补丁
npm audit fix

# 检查文件权限
find . -type f -perm 777
```

#### 2. 访问日志分析
```bash
# 分析访问模式
awk '{print $1}' access.log | sort | uniq -c | sort -nr

# 检查异常访问
grep "404\|500" access.log
```

#### 3. 数据备份验证
```bash
# 验证备份完整性
sqlite3 backup/pharmacy_20250629.db ".schema"

# 测试恢复流程
cp backup/pharmacy_20250629.db test_restore.db
sqlite3 test_restore.db "SELECT COUNT(*) FROM 销售订单;"
```

---

*本文档版本：v1.0.0 | 最后更新：2025-06-29 | 技术支持：<EMAIL>*
