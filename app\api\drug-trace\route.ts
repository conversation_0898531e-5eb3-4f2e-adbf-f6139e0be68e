import { NextRequest, NextResponse } from 'next/server';

/**
 * 处理药品追溯码查询请求
 * @param request - 请求对象
 * @returns 响应对象
 */
export async function GET(request: NextRequest) {
  try {
    // 从URL参数中获取条码
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');

    if (!code) {
      return NextResponse.json({
        success: false,
        message: '缺少条码参数'
      }, { status: 400 });
    }

    // 调用服务器端API识别条码类型
    let codeType;
    try {
      const typeResponse = await fetch(`${request.nextUrl.origin}/api/mashangfangxin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'identifyCodeType',
          params: { code }
        }),
      });

      // 检查响应状态
      if (!typeResponse.ok) {
        const errorText = await typeResponse.text();
        console.error('识别条码类型失败，服务器返回非200状态码:', typeResponse.status, errorText);
        return NextResponse.json({
          success: false,
          message: `识别条码类型失败，服务器返回状态码: ${typeResponse.status}`
        }, { status: 500 });
      }

      const typeData = await typeResponse.json();
      if (!typeData.success) {
        return NextResponse.json({
          success: false,
          message: '识别条码类型失败: ' + typeData.message
        }, { status: 500 });
      }

      codeType = typeData.data;

      // 检查是否是药品追溯码
      if (codeType !== 'traceCode') {
        return NextResponse.json({
          success: false,
          message: `扫描的不是药品追溯码，而是${codeType === 'barcode' ? '商品条形码' :
                                            codeType === 'supervisionCode' ? '药品监管码' :
                                            codeType === 'deviceUDI' ? '医疗器械UDI' : '未知类型条码'}`
        }, { status: 400 });
      }
    } catch (error) {
      console.error('识别条码类型失败:', error);
      return NextResponse.json({
        success: false,
        message: '识别条码类型失败: ' + ((error as Error).message || '未知错误')
      }, { status: 500 });
    }

    let drugInfo;
    let barcode;

    // 已确认是药品追溯码，继续处理
    // 先尝试获取对应的商品条码
    try {
        const barcodeResponse = await fetch(`${request.nextUrl.origin}/api/mashangfangxin`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'getBarcodeByTraceCode',
            params: { traceCode: code }
          }),
        });

        // 检查响应状态
        if (!barcodeResponse.ok) {
          const errorText = await barcodeResponse.text();
          console.error('获取商品条码失败，服务器返回非200状态码:', barcodeResponse.status, errorText);
          // 获取失败不影响后续流程
        } else {
          const barcodeData = await barcodeResponse.json();
          if (barcodeData.success) {
            barcode = barcodeData.data?.barcode;
          }
        }
      } catch (error) {
        console.error('获取商品条码失败:', error);
        // 获取失败不影响后续流程
      }

      // 获取药品追溯信息
      try {
        const drugResponse = await fetch(`${request.nextUrl.origin}/api/mashangfangxin`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'getDrugInfoByTraceCode',
            params: { code }
          }),
        });

        // 检查响应状态
        if (!drugResponse.ok) {
          const errorText = await drugResponse.text();
          console.error('获取药品追溯信息失败，服务器返回非200状态码:', drugResponse.status, errorText);
          return NextResponse.json({
            success: false,
            message: `获取药品追溯信息失败，服务器返回状态码: ${drugResponse.status}`
          }, { status: 500 });
        }

        const drugData = await drugResponse.json();
        if (drugData.success) {
          drugInfo = drugData.data;
        } else {
          return NextResponse.json({
            success: false,
            message: '获取药品追溯信息失败: ' + drugData.message
          }, { status: 500 });
        }
      } catch (error) {
        console.error('获取药品追溯信息失败:', error);
        return NextResponse.json({
          success: false,
          message: '获取药品追溯信息失败: ' + ((error as Error).message || '未知错误')
        }, { status: 500 });
      }

    return NextResponse.json({
      success: true,
      data: {
        codeType,
        barcode,
        drugInfo
      }
    });
  } catch (error) {
    console.error('药品追溯码查询失败:', error);
    return NextResponse.json({
      success: false,
      message: '药品追溯码查询失败: ' + ((error as Error).message || '未知错误')
    }, { status: 500 });
  }
}

/**
 * 处理药品追溯码与商品条码关联更新请求
 * @param request - 请求对象
 * @returns 响应对象
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { traceCode, barcode } = body;

    if (!traceCode || !barcode) {
      return NextResponse.json({
        success: false,
        message: '缺少追溯码或商品条码参数'
      }, { status: 400 });
    }

    // 更新追溯码与商品条码的关联关系
    let result;
    try {
      const updateResponse = await fetch(`${request.nextUrl.origin}/api/mashangfangxin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'updateBarcodeByTraceCode',
          params: { traceCode, barcode }
        }),
      });

      // 检查响应状态
      if (!updateResponse.ok) {
        const errorText = await updateResponse.text();
        console.error('更新追溯码与商品条码关联失败，服务器返回非200状态码:', updateResponse.status, errorText);
        return NextResponse.json({
          success: false,
          message: `更新追溯码与商品条码关联失败，服务器返回状态码: ${updateResponse.status}`
        }, { status: 500 });
      }

      const updateData = await updateResponse.json();
      if (!updateData.success) {
        return NextResponse.json({
          success: false,
          message: '更新追溯码与商品条码关联失败: ' + updateData.message
        }, { status: 500 });
      }

      result = updateData.data;
    } catch (error) {
      console.error('更新追溯码与商品条码关联失败:', error);
      return NextResponse.json({
        success: false,
        message: '更新追溯码与商品条码关联失败: ' + ((error as Error).message || '未知错误')
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('更新追溯码与商品条码关联失败:', error);
    return NextResponse.json({
      success: false,
      message: '更新追溯码与商品条码关联失败: ' + ((error as Error).message || '未知错误')
    }, { status: 500 });
  }
}
