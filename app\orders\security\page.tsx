'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { getCurrentUser, UserRole, Permission, ROLE_PERMISSIONS, hasPermission } from '@/lib/auth';

interface AuditLog {
  timestamp: string;
  userId: number;
  userName: string;
  action: string;
  target: string;
  details: string;
  result: string;
  userAgent: string;
  ipAddress: string;
}

export default function OrderSecurityPage() {
  const [currentUser] = useState(getCurrentUser());
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟审计日志数据
  useEffect(() => {
    const mockLogs: AuditLog[] = [
      {
        timestamp: new Date().toISOString(),
        userId: 1,
        userName: '系统管理员',
        action: '查看订单列表',
        target: '订单管理',
        details: '访问订单管理页面，查看所有订单',
        result: 'success',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '*************'
      },
      {
        timestamp: new Date(Date.now() - 300000).toISOString(),
        userId: 1,
        userName: '系统管理员',
        action: '查看订单详情',
        target: '订单编号:SO-001',
        details: '查看销售订单详细信息',
        result: 'success',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '*************'
      },
      {
        timestamp: new Date(Date.now() - 600000).toISOString(),
        userId: 1,
        userName: '系统管理员',
        action: '查看统计报表',
        target: '订单统计',
        details: '访问订单统计分析页面',
        result: 'success',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '*************'
      },
      {
        timestamp: new Date(Date.now() - 900000).toISOString(),
        userId: 2,
        userName: '药师001',
        action: '创建入库订单',
        target: '入库记录:RK-001',
        details: '创建新的药品入库记录',
        result: 'success',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '*************'
      },
      {
        timestamp: new Date(Date.now() - 1200000).toISOString(),
        userId: 3,
        userName: '收银员001',
        action: '创建销售订单',
        target: '销售订单:SO-002',
        details: '创建新的销售订单',
        result: 'success',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '*************'
      }
    ];
    
    setAuditLogs(mockLogs);
  }, []);

  // 格式化时间
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 获取操作结果样式
  const getResultStyle = (result: string) => {
    switch (result) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 检查是否有查看审计日志的权限
  if (!hasPermission(currentUser, Permission.VIEW_AUDIT_LOGS)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg mb-4">权限不足</div>
          <div className="text-gray-600 mb-4">您没有查看安全日志的权限</div>
          <Link
            href="/orders"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            返回订单管理
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/orders"
                  className="text-blue-600 hover:text-blue-800"
                >
                  ← 返回订单管理
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-blue-700">订单安全管理</h1>
                  <p className="mt-1 text-sm text-gray-600">
                    权限控制和操作日志管理
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 当前用户信息 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-blue-700">当前用户信息</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">用户ID</label>
                <div className="mt-1 text-sm text-blue-700">{currentUser.id}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">用户名</label>
                <div className="mt-1 text-sm text-gray-900">{currentUser.username}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">姓名</label>
                <div className="mt-1 text-sm text-gray-900">{currentUser.name}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">角色</label>
                <div className="mt-1">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {currentUser.role === UserRole.ADMIN ? '管理员' :
                     currentUser.role === UserRole.PHARMACIST ? '药师' :
                     currentUser.role === UserRole.CASHIER ? '收银员' : '查看者'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 权限列表 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-blue-700">当前权限</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentUser.permissions.map((permission, index) => {
                const permissionNames: Record<Permission, string> = {
                  [Permission.VIEW_ORDERS]: '查看订单',
                  [Permission.CREATE_ORDERS]: '创建订单',
                  [Permission.EDIT_ORDERS]: '编辑订单',
                  [Permission.DELETE_ORDERS]: '删除订单',
                  [Permission.VIEW_INVENTORY]: '查看库存',
                  [Permission.MANAGE_INVENTORY]: '管理库存',
                  [Permission.VIEW_PRODUCTS]: '查看药品',
                  [Permission.MANAGE_PRODUCTS]: '管理药品',
                  [Permission.VIEW_STATISTICS]: '查看统计',
                  [Permission.MANAGE_SYSTEM]: '系统管理',
                  [Permission.VIEW_AUDIT_LOGS]: '查看审计日志'
                };

                return (
                  <div key={index} className="flex items-center space-x-2">
                    <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-700">{permissionNames[permission]}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 角色权限对照表 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-blue-700">角色权限对照表</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    权限
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                    管理员
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                    药师
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                    收银员
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                    查看者
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.values(Permission).map((permission) => {
                  const permissionNames: Record<Permission, string> = {
                    [Permission.VIEW_ORDERS]: '查看订单',
                    [Permission.CREATE_ORDERS]: '创建订单',
                    [Permission.EDIT_ORDERS]: '编辑订单',
                    [Permission.DELETE_ORDERS]: '删除订单',
                    [Permission.VIEW_INVENTORY]: '查看库存',
                    [Permission.MANAGE_INVENTORY]: '管理库存',
                    [Permission.VIEW_PRODUCTS]: '查看药品',
                    [Permission.MANAGE_PRODUCTS]: '管理药品',
                    [Permission.VIEW_STATISTICS]: '查看统计',
                    [Permission.MANAGE_SYSTEM]: '系统管理',
                    [Permission.VIEW_AUDIT_LOGS]: '查看审计日志'
                  };

                  return (
                    <tr key={permission}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {permissionNames[permission]}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        {ROLE_PERMISSIONS[UserRole.ADMIN].includes(permission) ? (
                          <svg className="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        {ROLE_PERMISSIONS[UserRole.PHARMACIST].includes(permission) ? (
                          <svg className="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        {ROLE_PERMISSIONS[UserRole.CASHIER].includes(permission) ? (
                          <svg className="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        {ROLE_PERMISSIONS[UserRole.VIEWER].includes(permission) ? (
                          <svg className="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* 操作日志 */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-blue-700">最近操作日志</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    用户
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    操作
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    目标
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    详情
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    结果
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    IP地址
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {auditLogs.map((log, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTime(log.timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-700">
                      {log.userName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.action}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.target}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                      {log.details}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getResultStyle(log.result)}`}>
                        {log.result === 'success' ? '成功' : log.result === 'error' ? '失败' : log.result}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                      {log.ipAddress}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
