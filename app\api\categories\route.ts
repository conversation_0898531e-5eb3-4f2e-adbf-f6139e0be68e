import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 获取药品分类列表
export async function GET() {
  try {
    const 药品分类 = await query(
      'SELECT 编号 as id, 名称 as name, 描述 as description FROM 药品分类 ORDER BY 编号 ASC'
    );

    return NextResponse.json({
      success: true,
      data: 药品分类
    });
  } catch (error) {
    console.error('获取药品分类列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取药品分类列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}