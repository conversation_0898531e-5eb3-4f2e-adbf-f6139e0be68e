import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

// 获取供应商列表
export async function GET() {
  try {
    const 供应商列表 = await query(`
      SELECT
        编号 as id,
        名称 as name,
        联系人 as contact_person,
        电话 as phone,
        邮箱 as email,
        地址 as address,
        税号 as tax_number,
        银行账户 as bank_account,
        开户行 as bank_name,
        营业执照号 as license_number,
        执照有效期 as license_expiry_date,
        GSP证书号 as gsp_certificate,
        GSP有效期 as gsp_expiry_date,
        经营范围 as business_scope,
        质量负责人 as quality_officer,
        合作状态 as cooperation_status,
        状态 as status,
        备注 as notes,
        创建时间 as created_at,
        更新时间 as updated_at
      FROM 供应商
      ORDER BY 创建时间 DESC
    `);

    return NextResponse.json({
      success: true,
      data: 供应商列表
    });
  } catch (error) {
    console.error('获取供应商列表失败:', error);
    return NextResponse.json(
      { success: false, message: '获取供应商列表失败' },
      { status: 500 }
    );
  }
}

// 创建新供应商
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // 验证必填字段
    if (!data.name || !data.name.trim()) {
      return NextResponse.json(
        { success: false, message: '供应商名称不能为空' },
        { status: 400 }
      );
    }

    // 检查供应商名称是否已存在
    const existingSupplier = await query(
      'SELECT 编号, 名称 FROM 供应商 WHERE 名称 = ?',
      [data.name.trim()]
    );

    if (existingSupplier && existingSupplier.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: '供应商名称已存在',
          error: `供应商 "${data.name}" 已存在 (ID: ${existingSupplier[0].编号})`
        },
        { status: 400 }
      );
    }

    const result = await run(
      `INSERT INTO 供应商 (
        名称, 联系人, 电话, 邮箱, 地址,
        税号, 银行账户, 开户行, 营业执照号, 执照有效期,
        GSP证书号, GSP有效期, 经营范围, 质量负责人,
        合作状态, 状态, 备注
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.name?.trim() || '',
        data.contact_person?.trim() || '',
        data.phone?.trim() || '',
        data.email?.trim() || '',
        data.address?.trim() || '',
        data.tax_number?.trim() || '',
        data.bank_account?.trim() || '',
        data.bank_name?.trim() || '',
        data.license_number?.trim() || '',
        data.license_expiry_date || null,
        data.gsp_certificate?.trim() || '',
        data.gsp_expiry_date || null,
        data.business_scope?.trim() || '',
        data.quality_officer?.trim() || '',
        data.cooperation_status || 'active',
        data.status || 'active',
        data.notes?.trim() || ''
      ]
    );

    if (!result.lastID) {
      return NextResponse.json(
        { success: false, message: '创建供应商失败' },
        { status: 500 }
      );
    }

    // 获取新创建的供应商信息
    const newSupplier = await query(
      `SELECT
        编号 as id,
        名称 as name,
        联系人 as contact_person,
        电话 as phone,
        邮箱 as email,
        地址 as address,
        税号 as tax_number,
        银行账户 as bank_account,
        开户行 as bank_name,
        营业执照号 as license_number,
        执照有效期 as license_expiry_date,
        GSP证书号 as gsp_certificate,
        GSP有效期 as gsp_expiry_date,
        经营范围 as business_scope,
        质量负责人 as quality_officer,
        合作状态 as cooperation_status,
        状态 as status,
        备注 as notes,
        创建时间 as created_at,
        更新时间 as updated_at
      FROM 供应商 WHERE 编号 = ?`,
      [result.lastID]
    );

    return NextResponse.json({
      success: true,
      data: newSupplier[0],
      message: '供应商创建成功'
    });
  } catch (error) {
    console.error('添加供应商失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '添加供应商失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 更新供应商信息
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();

    if (!data.id) {
      return NextResponse.json(
        { success: false, message: '供应商ID不能为空' },
        { status: 400 }
      );
    }

    if (!data.name || !data.name.trim()) {
      return NextResponse.json(
        { success: false, message: '供应商名称不能为空' },
        { status: 400 }
      );
    }

    // 检查供应商是否存在
    const existingSupplier = await query(
      'SELECT 编号 FROM 供应商 WHERE 编号 = ?',
      [data.id]
    );

    if (!existingSupplier || existingSupplier.length === 0) {
      return NextResponse.json(
        { success: false, message: '供应商不存在' },
        { status: 404 }
      );
    }

    // 检查名称是否与其他供应商重复
    const nameCheck = await query(
      'SELECT 编号, 名称 FROM 供应商 WHERE 名称 = ? AND 编号 != ?',
      [data.name.trim(), data.id]
    );

    if (nameCheck && nameCheck.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: '供应商名称已存在',
          error: `供应商 "${data.name}" 已被其他供应商使用 (ID: ${nameCheck[0].编号})`
        },
        { status: 400 }
      );
    }

    await run(
      `UPDATE 供应商
      SET
        名称 = ?,
        联系人 = ?,
        电话 = ?,
        邮箱 = ?,
        地址 = ?,
        税号 = ?,
        开户行 = ?,
        银行账户 = ?,
        营业执照号 = ?,
        执照有效期 = ?,
        GSP证书号 = ?,
        GSP有效期 = ?,
        经营范围 = ?,
        质量负责人 = ?,
        合作状态 = ?,
        状态 = ?,
        备注 = ?,
        更新时间 = CURRENT_TIMESTAMP
      WHERE 编号 = ?`,
      [
        data.name?.trim() || '',
        data.contact_person?.trim() || '',
        data.phone?.trim() || '',
        data.email?.trim() || '',
        data.address?.trim() || '',
        data.tax_number?.trim() || '',
        data.bank_name?.trim() || '',
        data.bank_account?.trim() || '',
        data.license_number?.trim() || '',
        data.license_expiry_date || null,
        data.gsp_certificate?.trim() || '',
        data.gsp_expiry_date || null,
        data.business_scope?.trim() || '',
        data.quality_officer?.trim() || '',
        data.cooperation_status || 'active',
        data.status || 'active',
        data.notes?.trim() || '',
        data.id
      ]
    );

    // 获取更新后的供应商信息
    const updatedSupplier = await query(
      `SELECT
        编号 as id,
        名称 as name,
        联系人 as contact_person,
        电话 as phone,
        邮箱 as email,
        地址 as address,
        税号 as tax_number,
        银行账户 as bank_account,
        开户行 as bank_name,
        营业执照号 as license_number,
        执照有效期 as license_expiry_date,
        GSP证书号 as gsp_certificate,
        GSP有效期 as gsp_expiry_date,
        经营范围 as business_scope,
        质量负责人 as quality_officer,
        合作状态 as cooperation_status,
        状态 as status,
        备注 as notes,
        创建时间 as created_at,
        更新时间 as updated_at
      FROM 供应商 WHERE 编号 = ?`,
      [data.id]
    );

    return NextResponse.json({
      success: true,
      data: updatedSupplier[0],
      message: '供应商更新成功'
    });
  } catch (error) {
    console.error('更新供应商失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '更新供应商失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 删除供应商
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, message: '供应商ID不能为空' },
        { status: 400 }
      );
    }

    // 检查供应商是否存在
    const existingSupplier = await query(
      'SELECT 编号, 名称 FROM 供应商 WHERE 编号 = ?',
      [id]
    );

    if (!existingSupplier || existingSupplier.length === 0) {
      return NextResponse.json(
        { success: false, message: '供应商不存在' },
        { status: 404 }
      );
    }

    // 检查是否有药品关联到此供应商
    const relatedProducts = await query(
      'SELECT COUNT(*) as count FROM 药品信息 WHERE 供应商编号 = ?',
      [id]
    );

    if (relatedProducts && relatedProducts[0].count > 0) {
      return NextResponse.json(
        {
          success: false,
          message: '无法删除供应商',
          error: `该供应商下还有 ${relatedProducts[0].count} 个药品，请先处理相关药品后再删除`
        },
        { status: 400 }
      );
    }

    // 删除供应商
    await run('DELETE FROM 供应商 WHERE 编号 = ?', [id]);

    return NextResponse.json({
      success: true,
      message: `供应商 "${existingSupplier[0].名称}" 删除成功`
    });
  } catch (error) {
    console.error('删除供应商失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '删除供应商失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}