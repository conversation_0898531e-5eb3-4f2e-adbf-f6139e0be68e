import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * GET 请求处理函数 - 获取单个供应商详细信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { success: false, message: '供应商ID不能为空' },
        { status: 400 }
      );
    }

    const supplier = await query(
      `SELECT
        编号 as id,
        名称 as name,
        联系人 as contact_person,
        电话 as phone,
        邮箱 as email,
        地址 as address,
        税号 as tax_number,
        银行账户 as bank_account,
        开户行 as bank_name,
        营业执照号 as license_number,
        执照有效期 as license_expiry_date,
        GSP证书号 as gsp_certificate,
        GSP有效期 as gsp_expiry_date,
        经营范围 as business_scope,
        质量负责人 as quality_officer,
        合作状态 as cooperation_status,
        状态 as status,
        备注 as notes,
        创建时间 as created_at,
        更新时间 as updated_at
      FROM 供应商 WHERE 编号 = ?`,
      [id]
    );

    if (!supplier || supplier.length === 0) {
      return NextResponse.json(
        { success: false, message: '供应商不存在' },
        { status: 404 }
      );
    }

    // 获取该供应商关联的药品数量
    const productCount = await query(
      'SELECT COUNT(*) as count FROM 药品信息 WHERE 供应商编号 = ?',
      [id]
    );

    const supplierData = {
      ...supplier[0],
      product_count: productCount[0]?.count || 0
    };

    return NextResponse.json({
      success: true,
      data: supplierData
    });
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取供应商详情失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
