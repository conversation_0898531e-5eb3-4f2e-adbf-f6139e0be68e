import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const tableName = searchParams.get('table') || '销售订单';

    // 获取表结构
    const tableInfo = await query(`PRAGMA table_info(${tableName})`);
    
    // 获取表中的数据示例
    const sampleData = await query(`SELECT * FROM ${tableName} LIMIT 3`);

    return NextResponse.json({
      success: true,
      data: {
        tableName,
        structure: tableInfo,
        sampleData
      }
    });

  } catch (error) {
    console.error('查询表信息失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '查询表信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
