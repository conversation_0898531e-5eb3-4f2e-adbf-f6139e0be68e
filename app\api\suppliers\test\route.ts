import { NextResponse } from 'next/server';
import { run } from '@/lib/db';

/**
 * POST 请求处理函数 - 创建测试供应商数据
 */
export async function POST() {
  try {
    console.log('开始创建测试供应商数据...');

    // 创建测试供应商
    const testSuppliers = [
      {
        name: '北京同仁堂药业有限公司',
        contact_person: '张经理',
        phone: '010-********',
        email: '<EMAIL>',
        address: '北京市东城区前门大街19号',
        tax_number: '91110101MA001234XY',
        bank_account: '********90********9',
        bank_name: '中国银行北京分行',
        license_number: '91110101MA001234XY',
        license_expiry_date: '2025-12-31',
        gsp_certificate: 'GSP-BJ-2023-001',
        gsp_expiry_date: '2025-12-31',
        business_scope: '中药材、中成药、化学药品、生物制品批发',
        quality_officer: '李质量',
        cooperation_status: 'active',
        status: 'active',
        notes: '老牌中药企业，产品质量可靠'
      },
      {
        name: '上海医药集团股份有限公司',
        contact_person: '王总监',
        phone: '021-********',
        email: '<EMAIL>',
        address: '上海市浦东新区张江高科技园区',
        tax_number: '91310000MA001235XZ',
        bank_account: '9********09********',
        bank_name: '工商银行上海分行',
        license_number: '91310000MA001235XZ',
        license_expiry_date: '2026-06-30',
        gsp_certificate: 'GSP-SH-2023-002',
        gsp_expiry_date: '2026-06-30',
        business_scope: '化学药品、生物制品、医疗器械批发',
        quality_officer: '陈质量',
        cooperation_status: 'active',
        status: 'active',
        notes: '大型医药集团，供应链完善'
      },
      {
        name: '广州白云山制药总厂',
        contact_person: '刘主任',
        phone: '020-********',
        email: '<EMAIL>',
        address: '广州市白云区白云山路123号',
        tax_number: '91440000MA001236XA',
        bank_account: '****************',
        bank_name: '建设银行广州分行',
        license_number: '91440000MA001236XA',
        license_expiry_date: '2025-09-30',
        gsp_certificate: 'GSP-GZ-2023-003',
        gsp_expiry_date: '2025-09-30',
        business_scope: '中成药、化学药品生产销售',
        quality_officer: '黄质量',
        cooperation_status: 'active',
        status: 'active',
        notes: '知名制药企业，产品线丰富'
      }
    ];

    const createdSuppliers = [];

    for (const supplier of testSuppliers) {
      const result = await run(
        `INSERT INTO 供应商 (
          名称, 联系人, 电话, 邮箱, 地址,
          税号, 银行账户, 开户行, 营业执照号, 执照有效期,
          GSP证书号, GSP有效期, 经营范围, 质量负责人,
          合作状态, 状态, 备注
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          supplier.name,
          supplier.contact_person,
          supplier.phone,
          supplier.email,
          supplier.address,
          supplier.tax_number,
          supplier.bank_account,
          supplier.bank_name,
          supplier.license_number,
          supplier.license_expiry_date,
          supplier.gsp_certificate,
          supplier.gsp_expiry_date,
          supplier.business_scope,
          supplier.quality_officer,
          supplier.cooperation_status,
          supplier.status,
          supplier.notes
        ]
      );

      createdSuppliers.push({
        id: result.lastID,
        name: supplier.name
      });

      console.log(`成功创建供应商: ${supplier.name}, ID: ${result.lastID}`);
    }

    return NextResponse.json({
      success: true,
      message: '测试供应商数据创建成功',
      data: {
        created_count: createdSuppliers.length,
        suppliers: createdSuppliers
      }
    });

  } catch (error) {
    console.error('创建测试供应商数据失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '创建测试供应商数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
