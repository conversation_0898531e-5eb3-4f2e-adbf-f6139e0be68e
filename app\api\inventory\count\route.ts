import { NextRequest, NextResponse } from 'next/server';
import { getDbConnection, run, query } from '@/lib/db';

interface CountItem {
  product_id: number;
  product_name: string;
  system_quantity: number;
  actual_quantity: number;
  difference: number;
  notes: string;
}

interface InventoryCountRequest {
  items: CountItem[];
  count_date: string;
  notes: string;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as InventoryCountRequest;
    const { items, count_date, notes } = body;
    
    // 验证数据
    if (!items || !items.length) {
      return NextResponse.json({ 
        success: false, 
        message: '缺少盘点数据项目' 
      }, { status: 400 });
    }

    // 获取数据库连接
    const db = await getDbConnection();
    
    try {
      // 开始事务
      await run('BEGIN TRANSACTION');
      
      // 1. 创建盘点记录
      const countResult = await run(
        `INSERT INTO inventory_count (count_date, notes, item_count) 
         VALUES (?, ?, ?)`,
        [new Date(count_date).toISOString(), notes || '库存盘点', items.length]
      );
      
      const inventoryCountId = countResult.lastID;
      
      // 2. 为每个有差异的项目创建详细记录并更新库存
      for (const item of items) {
        // 创建盘点详情记录
        await run(
          `INSERT INTO inventory_count_detail 
           (inventory_count_id, product_id, system_quantity, actual_quantity, difference, notes) 
           VALUES (?, ?, ?, ?, ?, ?)`,
          [
            inventoryCountId,
            item.product_id,
            item.system_quantity,
            item.actual_quantity,
            item.difference,
            item.notes || ''
          ]
        );
        
        // 更新产品库存数量
        await run(
          `UPDATE product SET stock_quantity = ? WHERE id = ?`,
          [item.actual_quantity, item.product_id]
        );
      }
      
      // 提交事务
      await run('COMMIT');
      
      return NextResponse.json({ 
        success: true, 
        data: {
          inventory_count_id: inventoryCountId,
          items_count: items.length
        }
      });
    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('库存盘点处理错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理库存盘点时出错' 
    }, { status: 500 });
  }
} 