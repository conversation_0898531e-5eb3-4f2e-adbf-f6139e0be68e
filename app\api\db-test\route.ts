import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * GET 请求处理函数 - 测试数据库连接并返回基本数据
 */
export async function GET() {
  try {
    // 查询药品数据
    const 药品信息 = await query('SELECT * FROM 药品信息 LIMIT 10');
    
    // 查询分类数据
    const 药品分类 = await query('SELECT * FROM 药品分类');
    
    // 查询用户数据
    const users = await query('SELECT id, username, name, role FROM users');
    
    return NextResponse.json({
      success: true,
      message: '数据库连接成功',
      data: {
        药品信息,
        药品分类,
        users
      }
    });
  } catch (error) {
    console.error('数据库连接错误:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库连接失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}