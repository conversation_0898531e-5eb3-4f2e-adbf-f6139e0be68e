'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

// 订单类型映射
const orderTypeMap = {
  sales: '销售订单',
  stock_in: '入库订单',
  stock_out: '出库订单',
  inventory: '盘点订单',
  adjustment: '调整订单'
};

// 订单状态映射
const statusMap = {
  pending: '待处理',
  processing: '处理中',
  completed: '已完成',
  cancelled: '已取消',
  draft: '草稿',
  '已完成': '已完成',
  '待处理': '待处理',
  '处理中': '处理中',
  '已取消': '已取消',
  '草稿': '草稿'
};

interface Order {
  id: string;
  orderNumber: string;
  orderType: string;
  status: string;
  createdAt: string;
  operationDate: string;
  totalAmount?: number;
  note: string;
  operator: string;
  itemsCount: number;
}

interface FilterState {
  orderTypes: string[];
  status: string[];
  startDate: string;
  endDate: string;
  quickFilter: string;
  searchKeyword: string;
  searchType: string;
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [total, setTotal] = useState(0);
  const [limit] = useState(20);

  // 筛选状态
  const [filters, setFilters] = useState<FilterState>({
    orderTypes: [],
    status: [],
    startDate: '',
    endDate: '',
    quickFilter: '',
    searchKeyword: '',
    searchType: 'all'
  });

  // 显示筛选面板
  const [showFilters, setShowFilters] = useState(false);

  // 加载订单数据
  const loadOrders = async () => {
    try {
      setLoading(true);
      setError('');

      let url = '';
      let params = new URLSearchParams({
        page: currentPage.toString(),
        limit: limit.toString()
      });

      // 如果有搜索关键词，使用搜索API
      if (filters.searchKeyword.trim()) {
        url = '/api/orders/search-simple';
        params.append('keyword', filters.searchKeyword);
        params.append('type', filters.searchType);
      } else {
        // 否则使用统一查询API
        url = '/api/orders/unified-simple';
        
        if (filters.orderTypes.length > 0) {
          params.append('orderType', filters.orderTypes.join(','));
        }
        if (filters.status.length > 0) {
          params.append('status', filters.status.join(','));
        }
        if (filters.startDate) {
          params.append('startDate', filters.startDate);
        }
        if (filters.endDate) {
          params.append('endDate', filters.endDate);
        }
        if (filters.quickFilter) {
          params.append('quickFilter', filters.quickFilter);
        }
      }

      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.success) {
        setOrders(data.data.orders);
        setTotal(data.data.total);
        setTotalPages(data.data.totalPages);
      } else {
        setError(data.message || '加载订单失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
      console.error('加载订单失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    loadOrders();
  }, [currentPage, filters]);

  // 处理筛选变化
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // 重置到第一页
  };

  // 处理快捷筛选
  const handleQuickFilter = (filter: string) => {
    handleFilterChange({ quickFilter: filter, startDate: '', endDate: '' });
  };

  // 处理搜索
  const handleSearch = (keyword: string, type: string) => {
    handleFilterChange({ searchKeyword: keyword, searchType: type });
  };

  // 清除筛选
  const clearFilters = () => {
    setFilters({
      orderTypes: [],
      status: [],
      startDate: '',
      endDate: '',
      quickFilter: '',
      searchKeyword: '',
      searchType: 'all'
    });
    setCurrentPage(1);
  };

  // 格式化金额
  const formatAmount = (amount?: number) => {
    if (amount === null || amount === undefined) return '-';
    return `¥${amount.toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-blue-700">订单管理</h1>
                <p className="mt-1 text-sm text-gray-600">
                  管理所有类型的订单，包括销售订单、入库订单、出库订单等
                </p>
              </div>
              <div className="flex space-x-3">
                <Link
                  href="/sales/new"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  新建销售订单
                </Link>
                <Link
                  href="/inventory"
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                >
                  库存管理
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 筛选和搜索区域 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="p-4">
            {/* 快捷筛选按钮 */}
            <div className="flex flex-wrap gap-2 mb-4">
              <button
                onClick={() => handleQuickFilter('')}
                className={`px-3 py-1 rounded-full text-sm ${
                  !filters.quickFilter ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'
                } hover:bg-blue-200 transition-colors`}
              >
                全部
              </button>
              <button
                onClick={() => handleQuickFilter('today')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filters.quickFilter === 'today' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'
                } hover:bg-blue-200 transition-colors`}
              >
                今日
              </button>
              <button
                onClick={() => handleQuickFilter('week')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filters.quickFilter === 'week' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'
                } hover:bg-blue-200 transition-colors`}
              >
                本周
              </button>
              <button
                onClick={() => handleQuickFilter('month')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filters.quickFilter === 'month' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'
                } hover:bg-blue-200 transition-colors`}
              >
                本月
              </button>
            </div>

            {/* 搜索框 */}
            <div className="flex gap-3 mb-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="搜索订单编号、药品名称、条形码等..."
                  value={filters.searchKeyword}
                  onChange={(e) => handleFilterChange({ searchKeyword: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                />
              </div>
              <select
                value={filters.searchType}
                onChange={(e) => handleFilterChange({ searchType: e.target.value })}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
              >
                <option value="all">全部</option>
                <option value="orderNumber">订单编号</option>
                <option value="productName">药品名称</option>
                <option value="barcode">条形码</option>
                <option value="batchNumber">批次号</option>
              </select>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors text-blue-700"
              >
                高级筛选
              </button>
              {(filters.orderTypes.length > 0 || filters.status.length > 0 || filters.startDate || filters.endDate || filters.searchKeyword) && (
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  清除筛选
                </button>
              )}
            </div>

            {/* 高级筛选面板 */}
            {showFilters && (
              <div className="border-t pt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* 订单类型筛选 */}
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-2">订单类型</label>
                  <div className="space-y-1">
                    {Object.entries(orderTypeMap).map(([key, label]) => (
                      <label key={key} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.orderTypes.includes(key)}
                          onChange={(e) => {
                            const newTypes = e.target.checked
                              ? [...filters.orderTypes, key]
                              : filters.orderTypes.filter(t => t !== key);
                            handleFilterChange({ orderTypes: newTypes });
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">{label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* 状态筛选 */}
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-2">订单状态</label>
                  <div className="space-y-1">
                    {Object.entries(statusMap).map(([key, label]) => (
                      <label key={key} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.status.includes(key)}
                          onChange={(e) => {
                            const newStatus = e.target.checked
                              ? [...filters.status, key]
                              : filters.status.filter(s => s !== key);
                            handleFilterChange({ status: newStatus });
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">{label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* 日期范围 */}
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-2">开始日期</label>
                  <input
                    type="date"
                    value={filters.startDate}
                    onChange={(e) => handleFilterChange({ startDate: e.target.value, quickFilter: '' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-2">结束日期</label>
                  <input
                    type="date"
                    value={filters.endDate}
                    onChange={(e) => handleFilterChange({ endDate: e.target.value, quickFilter: '' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6 p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              共找到 <span className="font-medium text-blue-700">{total}</span> 条订单
            </div>
            <div className="text-sm text-gray-600">
              第 {currentPage} 页，共 {totalPages} 页
            </div>
          </div>
        </div>

        {/* 订单列表 */}
        <div className="bg-white rounded-lg shadow-sm border">
          {loading ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <div className="text-red-500">{error}</div>
              <button
                onClick={loadOrders}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                重试
              </button>
            </div>
          ) : orders.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">暂无订单数据</div>
            </div>
          ) : (
            <>
              {/* 桌面端表格 */}
              <div className="hidden md:block overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        订单编号
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        类型
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        金额
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orders.map((order) => (
                      <tr key={`${order.orderType}-${order.id}`} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-blue-700">{order.orderNumber}</div>
                          {order.note && (
                            <div className="text-xs text-gray-500 truncate max-w-xs">{order.note}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            {orderTypeMap[order.orderType as keyof typeof orderTypeMap] || order.orderType}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            order.status === '已完成' || order.status === 'completed' 
                              ? 'bg-green-100 text-green-800'
                              : order.status === '待处理' || order.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : order.status === '已取消' || order.status === 'cancelled'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {statusMap[order.status as keyof typeof statusMap] || order.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatAmount(order.totalAmount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(order.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Link
                            href={`/orders/${order.id}?type=${order.orderType}`}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            查看
                          </Link>
                          {order.orderType === 'sales' && (
                            <Link
                              href={`/sales/${order.id}`}
                              className="text-green-600 hover:text-green-900"
                            >
                              编辑
                            </Link>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 移动端卡片 */}
              <div className="md:hidden">
                {orders.map((order) => (
                  <div key={`${order.orderType}-${order.id}`} className="border-b border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-sm font-medium text-blue-700">{order.orderNumber}</div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        order.status === '已完成' || order.status === 'completed' 
                          ? 'bg-green-100 text-green-800'
                          : order.status === '待处理' || order.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : order.status === '已取消' || order.status === 'cancelled'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {statusMap[order.status as keyof typeof statusMap] || order.status}
                      </span>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {orderTypeMap[order.orderType as keyof typeof orderTypeMap] || order.orderType}
                      </span>
                      <div className="text-sm text-gray-900">{formatAmount(order.totalAmount)}</div>
                    </div>
                    <div className="text-xs text-gray-500 mb-2">{formatDate(order.createdAt)}</div>
                    {order.note && (
                      <div className="text-xs text-gray-500 mb-2 truncate">{order.note}</div>
                    )}
                    <div className="flex space-x-3">
                      <Link
                        href={`/orders/${order.id}?type=${order.orderType}`}
                        className="text-blue-600 hover:text-blue-900 text-sm"
                      >
                        查看详情
                      </Link>
                      {order.orderType === 'sales' && (
                        <Link
                          href={`/sales/${order.id}`}
                          className="text-green-600 hover:text-green-900 text-sm"
                        >
                          编辑
                        </Link>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页控件 */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      显示第 {(currentPage - 1) * limit + 1} 到 {Math.min(currentPage * limit, total)} 条，共 {total} 条
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 text-blue-700"
                      >
                        上一页
                      </button>
                      
                      {/* 页码按钮 */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }
                        
                        return (
                          <button
                            key={pageNum}
                            onClick={() => setCurrentPage(pageNum)}
                            className={`px-3 py-1 border rounded-md text-sm ${
                              currentPage === pageNum
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'border-gray-300 text-blue-700 hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                      
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 text-blue-700"
                      >
                        下一页
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
