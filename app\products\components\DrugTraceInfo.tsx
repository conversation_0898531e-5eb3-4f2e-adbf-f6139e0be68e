'use client';

import { useState } from 'react';
import { Dialog } from '@headlessui/react';

interface DrugInfo {
  drug_name?: string;
  approval_number?: string;
  manufacturer?: string;
  specification?: string;
  dosage_form?: string;
  production_date?: string;
  expiry_date?: string;
  batch_number?: string;
  [key: string]: any;
}

interface DrugTraceInfoProps {
  isOpen: boolean;
  onClose: () => void;
  drugInfo: DrugInfo | null;
  codeType: 'traceCode' | 'barcode' | 'supervisionCode' | 'deviceUDI' | null;
  code: string;
  onAddProduct?: (drugInfo: DrugInfo) => void;
}

/**
 * 药品追溯信息展示组件
 */
export default function DrugTraceInfo({
  isOpen,
  onClose,
  drugInfo,
  codeType,
  code,
  onAddProduct
}: DrugTraceInfoProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showFullJson, setShowFullJson] = useState<boolean>(false);

  // 处理添加药品到系统
  const handleAddProduct = () => {
    if (!drugInfo) return;

    setIsLoading(true);

    try {
      if (onAddProduct) {
        onAddProduct(drugInfo);
      }
      onClose();
    } catch (error) {
      console.error('添加药品失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full bg-white rounded-xl shadow-lg">
          <div className="p-6">
            <Dialog.Title className="text-lg font-medium leading-6 text-gray-900 mb-4 flex justify-between items-center">
              <span>
                {codeType === 'traceCode' ? '药品追溯信息' :
                 codeType === 'barcode' ? '药品信息' :
                 codeType === 'supervisionCode' ? '药品监管信息' :
                 codeType === 'deviceUDI' ? '医疗器械信息' : '产品信息'}
              </span>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </Dialog.Title>

            <div className="mb-4 p-3 bg-blue-50 rounded-md">
              <div className="flex flex-col space-y-2">
                <div className="flex items-center">
                  <span className="text-sm font-medium text-blue-700 mr-2">药品追溯码:</span>
                  <span className="text-sm text-blue-700">{code}</span>
                </div>
                {drugInfo?.barcode && (
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-blue-700 mr-2">商品条形码:</span>
                    <span className="text-sm text-blue-700">{drugInfo.barcode}</span>
                  </div>
                )}
                <div className="mt-2 text-xs text-gray-500">
                  <p>* 药品追溯码用于药品追溯系统，商品条形码用于商品识别</p>
                  <p>* 添加药品时，两种码都会保存到系统中</p>
                </div>
              </div>
            </div>

            {/* 调试信息区域 */}
            <div className="mb-4 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
              <div className="flex justify-between items-center mb-1">
                <div className="font-bold">调试信息:</div>
                <button
                  onClick={() => setShowFullJson(!showFullJson)}
                  className="text-blue-600 text-xs hover:text-blue-800"
                >
                  {showFullJson ? '隐藏完整JSON' : '显示完整JSON'}
                </button>
              </div>

              {showFullJson ? (
                <pre className="whitespace-pre-wrap break-all">
                  {JSON.stringify(drugInfo, null, 2)}
                </pre>
              ) : (
                <>
                  <div>条码类型: {codeType || '未知'}</div>
                  <div>条码: {code || '未知'}</div>
                  <div>数据结构: {drugInfo ? '有数据' : '无数据'}</div>
                  {drugInfo && (codeType === 'deviceUDI' || codeType === 'supervisionCode') && (
                    <>
                      <div>结果: {drugInfo.result ? '存在' : '不存在'}</div>
                      <div>模型: {drugInfo.result?.models ? '存在' : '不存在'}</div>
                      <div>详情: {drugInfo.result?.models?.code_full_info_dto ? '存在' : '不存在'}</div>
                      <div>详情[0]: {drugInfo.result?.models?.code_full_info_dto?.[0] ? '存在' : '不存在'}</div>
                      <div>药品基本信息: {drugInfo.result?.models?.code_full_info_dto?.[0]?.drug_ent_base_d_t_o ? '存在' : '不存在'}</div>
                      <div>消息代码: {drugInfo.result?.msg_code || '无'}</div>
                      <div>响应成功: {drugInfo.result?.response_success ? '是' : '否'}</div>
                    </>
                  )}
                </>
              )}
            </div>

            {drugInfo ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* 统一显示药品信息，根据API响应格式自动适配 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">药品名称</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.drug_ent_base_d_t_o?.physic_name ||
                       drugInfo.drug_name || '未知'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">批准文号</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.drug_ent_base_d_t_o?.approval_licence_no ||
                       drugInfo.approval_number || '未知'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">生产企业</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.p_user_ent_d_t_o?.ent_name ||
                       drugInfo.manufacturer || '未知'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">规格</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.drug_ent_base_d_t_o?.pkg_spec_crit ||
                       drugInfo.specification || '未知'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">剂型</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.drug_ent_base_d_t_o?.prepn_type_desc ||
                       drugInfo.dosage_form || '未知'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">批号</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.code_produce_info_d_t_o?.produce_info_list?.produce_info_dto?.[0]?.batch_no ||
                       drugInfo.batch_number || '未知'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">生产日期</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.code_produce_info_d_t_o?.produce_info_list?.produce_info_dto?.[0]?.produce_date_str ||
                       drugInfo.production_date || '未知'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">有效期至</label>
                    <div className="mt-1 text-blue-700">
                      {drugInfo.result?.models?.code_full_info_dto?.[0]?.code_produce_info_d_t_o?.produce_info_list?.produce_info_dto?.[0]?.expire_date ||
                       drugInfo.expiry_date || '未知'}
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    关闭
                  </button>

                  {onAddProduct && (
                    <button
                      type="button"
                      onClick={handleAddProduct}
                      disabled={isLoading}
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {isLoading ? '处理中...' : '添加到药品库'}
                    </button>
                  )}
                </div>
              </div>
            ) : (
              <div className="py-8 text-center text-gray-500">
                未能获取药品信息，请检查条码是否正确。
              </div>
            )}
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
