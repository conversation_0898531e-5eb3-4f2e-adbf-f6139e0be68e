'use client';

import { useEffect } from 'react';

/**
 * 数据库初始化组件
 * 在客户端组件中调用API初始化数据库
 */
export default function DbInitializer() {
  useEffect(() => {
    // 在客户端组件挂载时调用API初始化数据库
    const initDb = async () => {
      try {
        const response = await fetch('/api/init-db');
        const data = await response.json();
        
        if (data.success) {
          console.log('数据库初始化成功');
        } else {
          console.error('数据库初始化失败:', data.message);
        }
      } catch (error) {
        console.error('调用数据库初始化API失败:', error);
      }
    };
    
    initDb();
  }, []);
  
  // 这个组件不渲染任何内容
  return null;
}
