'use client';

import { useState, useEffect } from 'react';

interface Backup {
  fileName: string;
  size: number;
  created: string;
  timestamp: string;
}

export default function DatabaseManagement() {
  const [loading, setLoading] = useState(false);
  const [backups, setBackups] = useState<Backup[]>([]);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [showConfirmInitialize, setShowConfirmInitialize] = useState(false);
  const [showConfirmRestore, setShowConfirmRestore] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState<string | null>(null);

  // 获取备份列表
  const fetchBackups = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await fetch('/api/db-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'list-backups' }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setBackups(data.data.backups);
      } else {
        setError(data.message || '获取备份列表失败');
      }
    } catch (err) {
      setError('获取备份列表失败');
      console.error('获取备份列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据库
  const initializeDatabase = async () => {
    try {
      setLoading(true);
      setMessage('');
      setError('');
      
      const response = await fetch('/api/db-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'initialize' }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage('数据库初始化成功');
      } else {
        setError(data.message || '数据库初始化失败');
      }
    } catch (err) {
      setError('数据库初始化失败');
      console.error('数据库初始化失败:', err);
    } finally {
      setLoading(false);
      setShowConfirmInitialize(false);
    }
  };

  // 备份数据库
  const backupDatabase = async () => {
    try {
      setLoading(true);
      setMessage('');
      setError('');
      
      const response = await fetch('/api/db-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'backup' }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage(`数据库备份成功: ${data.data.backupFile}`);
        fetchBackups(); // 刷新备份列表
      } else {
        setError(data.message || '数据库备份失败');
      }
    } catch (err) {
      setError('数据库备份失败');
      console.error('数据库备份失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 恢复数据库
  const restoreDatabase = async (backupFile: string) => {
    try {
      setLoading(true);
      setMessage('');
      setError('');
      
      const response = await fetch('/api/db-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'restore',
          backupFile: backupFile
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage(`数据库恢复成功，使用备份: ${backupFile}`);
      } else {
        setError(data.message || '数据库恢复失败');
      }
    } catch (err) {
      setError('数据库恢复失败');
      console.error('数据库恢复失败:', err);
    } finally {
      setLoading(false);
      setShowConfirmRestore(false);
      setSelectedBackup(null);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  // 组件加载时获取备份列表
  useEffect(() => {
    fetchBackups();
  }, []);

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-6">
      <h2 className="text-xl font-semibold mb-4 text-blue-700">数据库管理</h2>
      
      {/* 消息提示 */}
      {message && (
        <div className="mb-4 p-3 bg-green-100 text-green-800 rounded-md">
          {message}
        </div>
      )}
      
      {/* 错误提示 */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-md">
          {error}
        </div>
      )}
      
      {/* 数据库操作按钮 */}
      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={() => setShowConfirmInitialize(true)}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
        >
          初始化数据库
        </button>
        
        <button
          onClick={backupDatabase}
          disabled={loading}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
        >
          备份数据库
        </button>
      </div>
      
      {/* 备份列表 */}
      <div>
        <h3 className="text-lg font-medium mb-3 text-blue-700">备份列表</h3>
        
        {loading && <div className="text-gray-500">加载中...</div>}
        
        {!loading && backups.length === 0 && (
          <div className="text-gray-500">暂无备份</div>
        )}
        
        {!loading && backups.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备份文件</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">大小</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {backups.map((backup) => (
                  <tr key={backup.fileName}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{backup.fileName}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatFileSize(backup.size)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(backup.created)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <button
                        onClick={() => {
                          setSelectedBackup(backup.fileName);
                          setShowConfirmRestore(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        恢复
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* 初始化确认对话框 */}
      {showConfirmInitialize && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">确认初始化数据库</h3>
            <p className="mb-4 text-gray-600">
              初始化数据库将创建所有必要的表结构。如果表已存在，将保留现有数据。
              <br /><br />
              <span className="font-medium">建议在初始化前先备份数据库。</span>
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmInitialize(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={initializeDatabase}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                确认初始化
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* 恢复确认对话框 */}
      {showConfirmRestore && selectedBackup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">确认恢复数据库</h3>
            <p className="mb-4 text-gray-600">
              您确定要从以下备份恢复数据库吗？
              <br /><br />
              <span className="font-medium">{selectedBackup}</span>
              <br /><br />
              <span className="text-red-600 font-medium">警告：此操作将覆盖当前数据库中的所有数据！</span>
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowConfirmRestore(false);
                  setSelectedBackup(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => restoreDatabase(selectedBackup)}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                确认恢复
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
