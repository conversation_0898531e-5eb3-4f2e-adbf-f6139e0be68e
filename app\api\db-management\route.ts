import { NextRequest, NextResponse } from 'next/server';
import { Database } from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';
import { query, run } from '@/lib/db';
import { ensureSettingsTable } from '@/lib/db-init';

// 数据库文件路径
const DB_PATH = './db/药店管理系统.db';
// 备份目录
const BACKUP_DIR = './db/backups';

/**
 * 初始化数据库 - 创建所有表结构
 */
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    // 根据不同的操作执行不同的功能
    switch (action) {
      case 'initialize':
        return await initializeDb();
      case 'backup':
        return await backupDb();
      case 'restore':
        const { backupFile } = await request.json();
        return await restoreDb(backupFile);
      case 'list-backups':
        return await listBackups();
      default:
        return NextResponse.json(
          { success: false, message: '未知操作' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('数据库管理操作失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库管理操作失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 初始化数据库
 */
async function initializeDb() {
  try {
    console.log('开始初始化数据库...');

    // 使用现有的初始化函数
    await ensureSettingsTable();

    console.log('数据库初始化成功');

    return NextResponse.json({
      success: true,
      message: '数据库初始化成功'
    });
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库初始化失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 备份数据库
 */
async function backupDb() {
  try {
    console.log('开始备份数据库...');
    
    // 确保备份目录存在
    if (!fs.existsSync(BACKUP_DIR)) {
      fs.mkdirSync(BACKUP_DIR, { recursive: true });
    }
    
    // 生成备份文件名 (使用时间戳)
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `backup-${timestamp}.sqlite`;
    const backupPath = path.join(BACKUP_DIR, backupFileName);
    
    // 打开源数据库
    const sourceDb = await open({
      filename: DB_PATH,
      driver: Database
    });
    
    // 创建备份文件
    fs.copyFileSync(DB_PATH, backupPath);
    
    // 关闭数据库连接
    await sourceDb.close();
    
    console.log(`数据库备份成功: ${backupPath}`);
    
    return NextResponse.json({
      success: true,
      message: '数据库备份成功',
      data: {
        backupFile: backupFileName,
        timestamp: timestamp,
        path: backupPath
      }
    });
  } catch (error) {
    console.error('数据库备份失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库备份失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 恢复数据库
 */
async function restoreDb(backupFile: string) {
  try {
    console.log(`开始从备份恢复数据库: ${backupFile}`);
    
    // 验证备份文件名
    if (!backupFile || !backupFile.startsWith('backup-') || !backupFile.endsWith('.sqlite')) {
      return NextResponse.json(
        { success: false, message: '无效的备份文件名' },
        { status: 400 }
      );
    }
    
    const backupPath = path.join(BACKUP_DIR, backupFile);
    
    // 检查备份文件是否存在
    if (!fs.existsSync(backupPath)) {
      return NextResponse.json(
        { success: false, message: '备份文件不存在' },
        { status: 404 }
      );
    }
    
    // 关闭所有数据库连接
    // 注意: 这里可能需要更复杂的逻辑来确保所有连接都已关闭
    
    // 替换当前数据库文件
    fs.copyFileSync(backupPath, DB_PATH);
    
    console.log('数据库恢复成功');
    
    return NextResponse.json({
      success: true,
      message: '数据库恢复成功',
      data: {
        restoredFrom: backupFile
      }
    });
  } catch (error) {
    console.error('数据库恢复失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库恢复失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 列出所有备份
 */
async function listBackups() {
  try {
    console.log('获取备份列表...');
    
    // 确保备份目录存在
    if (!fs.existsSync(BACKUP_DIR)) {
      fs.mkdirSync(BACKUP_DIR, { recursive: true });
      return NextResponse.json({
        success: true,
        data: {
          backups: []
        }
      });
    }
    
    // 读取备份目录中的文件
    const files = fs.readdirSync(BACKUP_DIR);
    
    // 过滤出备份文件并获取文件信息
    const backups = files
      .filter(file => file.startsWith('backup-') && file.endsWith('.sqlite'))
      .map(file => {
        const filePath = path.join(BACKUP_DIR, file);
        const stats = fs.statSync(filePath);
        
        // 从文件名中提取时间戳
        const timestamp = file.replace('backup-', '').replace('.sqlite', '');
        
        return {
          fileName: file,
          size: stats.size,
          created: stats.birthtime,
          timestamp: timestamp
        };
      })
      .sort((a, b) => b.created.getTime() - a.created.getTime()); // 按创建时间降序排序
    
    return NextResponse.json({
      success: true,
      data: {
        backups
      }
    });
  } catch (error) {
    console.error('获取备份列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取备份列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
