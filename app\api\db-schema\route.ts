import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * GET 请求处理函数 - 查看数据库表结构
 */
export async function GET() {
  try {
    // 查询所有表
    const tables = await query(
      "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
    );
    
    const schema: any = {};
    
    // 为每个表获取结构信息
    for (const table of tables) {
      try {
        // 获取表结构
        const tableInfo = await query(`PRAGMA table_info(${table.name})`);
        schema[table.name] = {
          columns: tableInfo,
          sample_data: []
        };
        
        // 获取样本数据（最多3条）
        try {
          const sampleData = await query(`SELECT * FROM ${table.name} LIMIT 3`);
          schema[table.name].sample_data = sampleData;
        } catch (sampleError) {
          console.log(`无法获取表 ${table.name} 的样本数据:`, sampleError);
        }
      } catch (tableError) {
        console.log(`无法获取表 ${table.name} 的结构:`, tableError);
        schema[table.name] = { error: tableError.message };
      }
    }
    
    return NextResponse.json({
      success: true,
      message: '数据库结构查询成功',
      data: {
        tables: tables.map(t => t.name),
        schema
      }
    });
  } catch (error) {
    console.error('数据库结构查询错误:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库结构查询失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
