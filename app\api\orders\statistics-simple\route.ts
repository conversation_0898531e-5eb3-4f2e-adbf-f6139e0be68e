import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 简化版订单统计API
 * GET /api/orders/statistics-simple?period=today|week|month|quarter|year
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || 'month';

    // 构建日期筛选条件
    const now = new Date();
    let startDateTime = '';
    let endDateTime = now.toISOString().split('T')[0] + ' 23:59:59';
    
    switch (period) {
      case 'today':
        startDateTime = now.toISOString().split('T')[0] + ' 00:00:00';
        break;
      case 'week':
        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
        startDateTime = weekStart.toISOString().split('T')[0] + ' 00:00:00';
        break;
      case 'month':
        startDateTime = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0] + ' 00:00:00';
        break;
      case 'quarter':
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        startDateTime = quarterStart.toISOString().split('T')[0] + ' 00:00:00';
        break;
      case 'year':
        startDateTime = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0] + ' 00:00:00';
        break;
      default:
        startDateTime = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0] + ' 00:00:00';
    }

    // 1. 销售订单统计
    const salesStats = await query(`
      SELECT 
        COUNT(*) as order_count,
        COALESCE(SUM(总金额), 0) as total_amount,
        COALESCE(AVG(总金额), 0) as avg_amount,
        COUNT(CASE WHEN 状态 = '已完成' THEN 1 END) as completed_count,
        COUNT(CASE WHEN 状态 = '待处理' THEN 1 END) as pending_count,
        COUNT(CASE WHEN 状态 = '已取消' THEN 1 END) as cancelled_count
      FROM 销售订单 
      WHERE 创建时间 BETWEEN ? AND ?
    `, [startDateTime, endDateTime]);

    // 2. 库存操作统计
    const inventoryStats = await query(`
      SELECT 
        操作类型,
        COUNT(*) as operation_count,
        COALESCE(SUM(ABS(数量变化)), 0) as total_quantity
      FROM 库存记录 
      WHERE 操作时间 BETWEEN ? AND ?
      GROUP BY 操作类型
    `, [startDateTime, endDateTime]);

    // 3. 每日趋势统计（最近7天）
    const dailyTrends = await query(`
      SELECT 
        DATE(创建时间) as date,
        COUNT(*) as sales_count,
        COALESCE(SUM(总金额), 0) as sales_amount
      FROM 销售订单 
      WHERE 创建时间 >= DATE('now', '-7 days')
      GROUP BY DATE(创建时间)
      ORDER BY date DESC
      LIMIT 7
    `);

    // 4. 订单类型分布
    const orderTypeDistribution = [
      {
        type: 'sales',
        name: '销售订单',
        count: salesStats[0]?.order_count || 0,
        amount: salesStats[0]?.total_amount || 0
      }
    ];

    // 添加库存操作分布
    inventoryStats.forEach((stat: any) => {
      let typeName = '';
      let typeKey = '';
      switch (stat.操作类型) {
        case '入库':
          typeName = '入库订单';
          typeKey = 'stock_in';
          break;
        case '出库':
          typeName = '出库订单';
          typeKey = 'stock_out';
          break;
        case '盘点':
          typeName = '盘点订单';
          typeKey = 'inventory';
          break;
        case '调整':
          typeName = '调整订单';
          typeKey = 'adjustment';
          break;
      }
      
      if (typeName) {
        orderTypeDistribution.push({
          type: typeKey,
          name: typeName,
          count: stat.operation_count,
          amount: 0 // 库存操作没有金额
        });
      }
    });

    // 5. 月度对比统计（最近6个月）
    const monthlyComparison = await query(`
      SELECT 
        strftime('%Y-%m', 创建时间) as month,
        COUNT(*) as order_count,
        COALESCE(SUM(总金额), 0) as total_amount
      FROM 销售订单 
      WHERE 创建时间 >= DATE('now', '-6 months')
      GROUP BY strftime('%Y-%m', 创建时间)
      ORDER BY month DESC
      LIMIT 6
    `);

    // 格式化统计结果
    const statistics = {
      // 总体统计
      overview: {
        totalOrders: (salesStats[0]?.order_count || 0) + inventoryStats.reduce((sum: number, stat: any) => sum + stat.operation_count, 0),
        totalSalesAmount: salesStats[0]?.total_amount || 0,
        avgOrderAmount: salesStats[0]?.avg_amount || 0,
        completedOrders: salesStats[0]?.completed_count || 0,
        pendingOrders: salesStats[0]?.pending_count || 0,
        cancelledOrders: salesStats[0]?.cancelled_count || 0
      },

      // 销售统计
      sales: {
        orderCount: salesStats[0]?.order_count || 0,
        totalAmount: salesStats[0]?.total_amount || 0,
        avgAmount: salesStats[0]?.avg_amount || 0,
        completedCount: salesStats[0]?.completed_count || 0,
        pendingCount: salesStats[0]?.pending_count || 0,
        cancelledCount: salesStats[0]?.cancelled_count || 0
      },

      // 库存操作统计
      inventory: inventoryStats.map((stat: any) => ({
        operationType: stat.操作类型,
        operationCount: stat.operation_count,
        totalQuantity: stat.total_quantity
      })),

      // 订单类型分布
      orderTypeDistribution,

      // 每日趋势
      dailyTrends: dailyTrends.reverse().map((trend: any) => ({
        date: trend.date,
        salesCount: trend.sales_count,
        salesAmount: parseFloat(trend.sales_amount || '0')
      })),

      // 月度对比
      monthlyComparison: monthlyComparison.reverse().map((month: any) => ({
        month: month.month,
        orderCount: month.order_count,
        totalAmount: parseFloat(month.total_amount || '0')
      })),

      // 简化的热销信息（基于库存记录）
      topProducts: []
    };

    return NextResponse.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    console.error('订单统计查询失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '统计查询失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
