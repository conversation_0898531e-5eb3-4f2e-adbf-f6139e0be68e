import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 简化版订单搜索API
 * GET /api/orders/search-simple?keyword=xxx&type=all&page=1&limit=20
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const keyword = searchParams.get('keyword') || '';
    const searchType = searchParams.get('type') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (!keyword.trim()) {
      return NextResponse.json({
        success: true,
        data: {
          orders: [],
          total: 0,
          page,
          limit,
          totalPages: 0
        }
      });
    }

    const offset = (page - 1) * limit;
    const searchKeyword = `%${keyword.trim()}%`;

    let searchResults: any[] = [];

    // 根据搜索类型构建查询
    switch (searchType) {
      case 'orderNumber':
        // 搜索订单编号
        const orderNumberResults = await query(`
          SELECT 'sales' as order_type, CAST(编号 AS TEXT) as id, 订单编号 as order_number, 状态 as status, 创建时间 as created_at, 总金额 as total_amount, 备注 as note
          FROM 销售订单 WHERE 订单编号 LIKE ?
          UNION ALL
          SELECT 
            CASE 操作类型
              WHEN '入库' THEN 'stock_in'
              WHEN '出库' THEN 'stock_out'
              WHEN '盘点' THEN 'inventory'
              WHEN '调整' THEN 'adjustment'
            END as order_type,
            CAST(编号 AS TEXT) as id,
            COALESCE(码上放心单据号, 'SYS-' || 编号) as order_number,
            '已完成' as status,
            操作时间 as created_at,
            NULL as total_amount,
            备注 as note
          FROM 库存记录 WHERE COALESCE(码上放心单据号, 'SYS-' || 编号) LIKE ?
          ORDER BY created_at DESC
          LIMIT ? OFFSET ?
        `, [searchKeyword, searchKeyword, limit, offset]);
        searchResults = orderNumberResults;
        break;

      case 'productName':
        // 搜索药品名称（仅在销售订单中）
        const productNameResults = await query(`
          SELECT DISTINCT 'sales' as order_type, CAST(so.编号 AS TEXT) as id, so.订单编号 as order_number, so.状态 as status, so.创建时间 as created_at, so.总金额 as total_amount, so.备注 as note
          FROM 销售订单 so
          JOIN 订单明细 od ON so.编号 = od.order_id
          JOIN 药品信息 p ON od.product_id = p.编号
          WHERE p.名称 LIKE ?
          ORDER BY so.创建时间 DESC
          LIMIT ? OFFSET ?
        `, [searchKeyword, limit, offset]);
        searchResults = productNameResults;
        break;

      case 'barcode':
        // 搜索条形码（仅在销售订单中）
        const barcodeResults = await query(`
          SELECT DISTINCT 'sales' as order_type, CAST(so.编号 AS TEXT) as id, so.订单编号 as order_number, so.状态 as status, so.创建时间 as created_at, so.总金额 as total_amount, so.备注 as note
          FROM 销售订单 so
          JOIN 订单明细 od ON so.编号 = od.order_id
          JOIN 药品信息 p ON od.product_id = p.编号
          WHERE p.条形码 LIKE ?
          ORDER BY so.创建时间 DESC
          LIMIT ? OFFSET ?
        `, [searchKeyword, limit, offset]);
        searchResults = barcodeResults;
        break;

      case 'batchNumber':
        // 搜索批次号（仅在库存记录中）
        const batchResults = await query(`
          SELECT 
            CASE 操作类型
              WHEN '入库' THEN 'stock_in'
              WHEN '出库' THEN 'stock_out'
              WHEN '盘点' THEN 'inventory'
              WHEN '调整' THEN 'adjustment'
            END as order_type,
            CAST(编号 AS TEXT) as id,
            COALESCE(码上放心单据号, 'SYS-' || 编号) as order_number,
            '已完成' as status,
            操作时间 as created_at,
            NULL as total_amount,
            备注 as note
          FROM 库存记录 WHERE 批次号 LIKE ?
          ORDER BY 操作时间 DESC
          LIMIT ? OFFSET ?
        `, [searchKeyword, limit, offset]);
        searchResults = batchResults;
        break;

      default:
        // 全文搜索
        const allResults = await query(`
          SELECT 'sales' as order_type, CAST(编号 AS TEXT) as id, 订单编号 as order_number, 状态 as status, 创建时间 as created_at, 总金额 as total_amount, 备注 as note
          FROM 销售订单 WHERE 订单编号 LIKE ? OR 备注 LIKE ?
          UNION ALL
          SELECT 
            CASE 操作类型
              WHEN '入库' THEN 'stock_in'
              WHEN '出库' THEN 'stock_out'
              WHEN '盘点' THEN 'inventory'
              WHEN '调整' THEN 'adjustment'
            END as order_type,
            CAST(编号 AS TEXT) as id,
            COALESCE(码上放心单据号, 'SYS-' || 编号) as order_number,
            '已完成' as status,
            操作时间 as created_at,
            NULL as total_amount,
            备注 as note
          FROM 库存记录 WHERE COALESCE(码上放心单据号, 'SYS-' || 编号) LIKE ? OR 备注 LIKE ? OR 批次号 LIKE ?
          ORDER BY created_at DESC
          LIMIT ? OFFSET ?
        `, [searchKeyword, searchKeyword, searchKeyword, searchKeyword, searchKeyword, limit, offset]);
        searchResults = allResults;
        break;
    }

    // 获取总数（简化版本，使用结果数量）
    const total = searchResults.length;

    // 格式化结果
    const formattedResults = searchResults.map((result: any) => ({
      id: result.id,
      orderNumber: result.order_number,
      orderType: result.order_type,
      status: result.status,
      createdAt: result.created_at,
      operationDate: result.created_at.split(' ')[0],
      totalAmount: result.total_amount ? parseFloat(result.total_amount) : null,
      note: result.note || '',
      operator: '系统操作员',
      itemsCount: 1
    }));

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        orders: formattedResults,
        total,
        page,
        limit,
        totalPages,
        keyword,
        searchType
      }
    });

  } catch (error) {
    console.error('订单搜索失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '搜索失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
