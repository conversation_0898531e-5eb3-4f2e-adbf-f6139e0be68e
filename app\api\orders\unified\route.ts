import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 订单类型枚举
enum OrderType {
  SALES = 'sales',
  STOCK_IN = 'stock_in',
  STOCK_OUT = 'stock_out',
  INVENTORY = 'inventory',
  ADJUSTMENT = 'adjustment'
}

// 统一订单接口
interface UnifiedOrder {
  id: string;
  orderNumber: string;
  orderType: OrderType;
  status: string;
  createdAt: string;
  operationDate: string;
  totalAmount?: number;
  discountAmount?: number;
  payableAmount?: number;
  receivedAmount?: number;
  operator: string;
  note?: string;
  customerId?: number;
  customerName?: string;
  supplierId?: number;
  supplierName?: string;
  itemsCount: number;
  batchNumber?: string;
  traceCodesCount?: number;
  paymentMethod?: string;
}

// 查询参数接口
interface QueryParams {
  page?: number;
  limit?: number;
  orderType?: string[];
  status?: string[];
  startDate?: string;
  endDate?: string;
  search?: string;
  searchType?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * 统一订单查询API
 * GET /api/orders/unified
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // 解析查询参数
    const params: QueryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      orderType: searchParams.get('orderType')?.split(',') || [],
      status: searchParams.get('status')?.split(',') || [],
      startDate: searchParams.get('startDate') || '',
      endDate: searchParams.get('endDate') || '',
      search: searchParams.get('search') || '',
      searchType: searchParams.get('searchType') || 'all',
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    };

    // 构建查询条件
    const whereConditions: string[] = [];
    const queryParams: any[] = [];

    // 日期筛选
    if (params.startDate) {
      whereConditions.push('created_at >= ?');
      queryParams.push(params.startDate + ' 00:00:00');
    }
    if (params.endDate) {
      whereConditions.push('created_at <= ?');
      queryParams.push(params.endDate + ' 23:59:59');
    }

    // 状态筛选
    if (params.status && params.status.length > 0) {
      const statusPlaceholders = params.status.map(() => '?').join(', ');
      whereConditions.push(`status IN (${statusPlaceholders})`);
      queryParams.push(...params.status);
    }

    // 构建销售订单查询
    let salesOrderQuery = '';
    if (params.orderType.length === 0 || params.orderType.includes('sales')) {
      salesOrderQuery = `
        SELECT
          'sales' as order_type,
          CAST(编号 AS TEXT) as id,
          订单编号 as order_number,
          状态 as status,
          总金额 as total_amount,
          COALESCE(折扣金额, 0) as discount_amount,
          COALESCE(应付金额, 总金额) as payable_amount,
          COALESCE(实收金额, 总金额) as received_amount,
          COALESCE(支付方式, 'cash') as payment_method,
          创建时间 as created_at,
          COALESCE(备注, '') as note,
          COALESCE(操作员编号, 1) as operator_id,
          客户编号 as customer_id,
          (SELECT COUNT(*) FROM 订单明细 WHERE order_id = 销售订单.编号) as items_count
        FROM 销售订单
        ${whereConditions.length > 0 ? 'WHERE ' + whereConditions.map(cond =>
          cond.includes('created_at') ? cond.replace('created_at', '创建时间') :
          cond.includes('status') ? cond.replace('status', '状态') : cond
        ).join(' AND ') : ''}
      `;
    }

    // 构建库存记录查询
    let inventoryQuery = '';
    const inventoryTypes = params.orderType.filter(type => 
      ['stock_in', 'stock_out', 'inventory', 'adjustment'].includes(type)
    );
    
    if (params.orderType.length === 0 || inventoryTypes.length > 0) {
      const operationTypeConditions: string[] = [];
      
      if (params.orderType.length === 0 || params.orderType.includes('stock_in')) {
        operationTypeConditions.push("'入库'");
      }
      if (params.orderType.length === 0 || params.orderType.includes('stock_out')) {
        operationTypeConditions.push("'出库'");
      }
      if (params.orderType.length === 0 || params.orderType.includes('inventory')) {
        operationTypeConditions.push("'盘点'");
      }
      if (params.orderType.length === 0 || params.orderType.includes('adjustment')) {
        operationTypeConditions.push("'调整'");
      }

      const inventoryWhereConditions = [...whereConditions];
      if (operationTypeConditions.length > 0) {
        inventoryWhereConditions.push(`操作类型 IN (${operationTypeConditions.join(', ')})`);
      }

      inventoryQuery = `
        SELECT 
          CASE 操作类型
            WHEN '入库' THEN 'stock_in'
            WHEN '出库' THEN 'stock_out'
            WHEN '盘点' THEN 'inventory'
            WHEN '调整' THEN 'adjustment'
          END as order_type,
          CAST(编号 AS TEXT) as id,
          COALESCE(码上放心单据号, 'SYS-' || 编号) as order_number,
          '已完成' as status,
          NULL as total_amount,
          NULL as discount_amount,
          NULL as payable_amount,
          NULL as received_amount,
          NULL as payment_method,
          操作时间 as created_at,
          备注 as note,
          NULL as operator_id,
          NULL as customer_id,
          1 as items_count
        FROM 库存记录
        ${inventoryWhereConditions.length > 0 ? 'WHERE ' + inventoryWhereConditions.map(cond => 
          cond.includes('created_at') ? cond.replace('created_at', '操作时间') : cond
        ).join(' AND ') : ''}
      `;
    }

    // 合并查询
    const queries = [salesOrderQuery, inventoryQuery].filter(q => q);
    if (queries.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          orders: [],
          total: 0,
          page: params.page,
          limit: params.limit,
          totalPages: 0
        }
      });
    }

    const unionQuery = queries.join(' UNION ALL ');
    
    // 添加排序
    const orderByClause = `ORDER BY created_at ${params.sortOrder?.toUpperCase() || 'DESC'}`;
    
    // 添加分页
    const offset = ((params.page || 1) - 1) * (params.limit || 20);
    const limitClause = `LIMIT ${params.limit} OFFSET ${offset}`;
    
    const finalQuery = `${unionQuery} ${orderByClause} ${limitClause}`;

    // 执行查询
    const orders = await query(finalQuery, [...queryParams, ...queryParams]);

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM (${unionQuery}) as unified_orders`;
    const countResult = await query(countQuery, [...queryParams, ...queryParams]);
    const total = countResult[0]?.total || 0;

    // 格式化订单数据
    const formattedOrders: UnifiedOrder[] = await Promise.all(
      orders.map(async (order: any) => {
        const baseOrder: UnifiedOrder = {
          id: order.id,
          orderNumber: order.order_number,
          orderType: order.order_type as OrderType,
          status: order.status,
          createdAt: order.created_at,
          operationDate: order.created_at.split(' ')[0],
          operator: order.operator_id ? `操作员${order.operator_id}` : '系统操作员',
          note: order.note || '',
          itemsCount: order.items_count || 0
        };

        // 销售订单特有字段
        if (order.order_type === 'sales') {
          baseOrder.totalAmount = parseFloat(order.total_amount || '0');
          baseOrder.discountAmount = parseFloat(order.discount_amount || '0');
          baseOrder.payableAmount = parseFloat(order.payable_amount || '0');
          baseOrder.receivedAmount = parseFloat(order.received_amount || '0');
          baseOrder.paymentMethod = order.payment_method;
          baseOrder.customerId = order.customer_id;
          
          // 获取客户信息
          if (order.customer_id) {
            try {
              const customerResult = await query(
                'SELECT name FROM customers WHERE id = ?',
                [order.customer_id]
              );
              if (customerResult.length > 0) {
                baseOrder.customerName = customerResult[0].name;
              }
            } catch (error) {
              console.error('获取客户信息失败:', error);
            }
          }
        }

        // 库存记录特有字段
        if (['stock_in', 'stock_out', 'inventory', 'adjustment'].includes(order.order_type)) {
          try {
            // 获取库存记录详细信息
            const inventoryDetail = await query(
              'SELECT 批次号, 供应商编号, (SELECT COUNT(*) FROM 药品追溯码记录 WHERE 库存记录编号 = ?) as trace_codes_count FROM 库存记录 WHERE 编号 = ?',
              [order.id, order.id]
            );
            
            if (inventoryDetail.length > 0) {
              baseOrder.batchNumber = inventoryDetail[0].批次号;
              baseOrder.traceCodesCount = inventoryDetail[0].trace_codes_count || 0;
              baseOrder.supplierId = inventoryDetail[0].供应商编号;
              
              // 获取供应商信息
              if (inventoryDetail[0].供应商编号) {
                const supplierResult = await query(
                  'SELECT 名称 FROM 供应商信息表 WHERE 编号 = ?',
                  [inventoryDetail[0].供应商编号]
                );
                if (supplierResult.length > 0) {
                  baseOrder.supplierName = supplierResult[0].名称;
                }
              }
            }
          } catch (error) {
            console.error('获取库存记录详细信息失败:', error);
          }
        }

        return baseOrder;
      })
    );

    const totalPages = Math.ceil(total / (params.limit || 20));

    return NextResponse.json({
      success: true,
      data: {
        orders: formattedOrders,
        total,
        page: params.page || 1,
        limit: params.limit || 20,
        totalPages
      }
    });

  } catch (error) {
    console.error('统一订单查询失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '查询订单失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
