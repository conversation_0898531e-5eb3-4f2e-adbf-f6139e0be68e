@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  table {
    width: 100%;
    overflow-x: auto;
  }
  th, td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
  
  .mobile-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (min-width: 769px) {
  .container {
    padding: 0 2rem;
  }
  th, td {
    padding: 1rem;
    font-size: 1rem;
  }
}

/* 表格行悬停效果 */
.hover-row:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* 批次效果 */
.batch-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.batch-tag-normal {
  background-color: #ecfdf5;
  color: #047857;
}

.batch-tag-warning {
  background-color: #fffbeb;
  color: #b45309;
}

.batch-tag-alert {
  background-color: #fef2f2;
  color: #b91c1c;
}

/* 库存状态标签 */
.stock-level {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.stock-level-normal {
  background-color: #d1fae5;
  color: #047857;
}

.stock-level-low {
  background-color: #fef3c7;
  color: #b45309;
}

.stock-level-out {
  background-color: #fee2e2;
  color: #b91c1c;
}

/* 统计卡片动画 */
.stat-card {
  transition: all 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 滚动条美化 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}