'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  duration?: number;
}

export default function OrderTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [overallStatus, setOverallStatus] = useState<'idle' | 'running' | 'completed'>('idle');

  // 测试用例定义
  const testCases = [
    {
      name: '统一订单查询API测试',
      test: async () => {
        const response = await fetch('/api/orders/unified-simple?page=1&limit=5');
        const data = await response.json();
        if (!data.success) throw new Error(data.message);
        if (!Array.isArray(data.data.orders)) throw new Error('返回数据格式错误');
        return `成功获取 ${data.data.orders.length} 条订单记录`;
      }
    },
    {
      name: '订单筛选功能测试',
      test: async () => {
        const response = await fetch('/api/orders/unified-simple?page=1&limit=5&orderType=sales');
        const data = await response.json();
        if (!data.success) throw new Error(data.message);
        return `销售订单筛选成功，共 ${data.data.total} 条记录`;
      }
    },
    {
      name: '订单搜索功能测试',
      test: async () => {
        const response = await fetch('/api/orders/search-simple?keyword=SYS&type=orderNumber&page=1&limit=5');
        const data = await response.json();
        if (!data.success) throw new Error(data.message);
        return `搜索功能正常，找到 ${data.data.orders.length} 条匹配记录`;
      }
    },
    {
      name: '快捷筛选功能测试',
      test: async () => {
        const response = await fetch('/api/orders/unified-simple?page=1&limit=5&quickFilter=today');
        const data = await response.json();
        if (!data.success) throw new Error(data.message);
        return `今日订单筛选成功，共 ${data.data.total} 条记录`;
      }
    },
    {
      name: '订单统计API测试',
      test: async () => {
        const response = await fetch('/api/orders/statistics-simple?period=month');
        const data = await response.json();
        if (!data.success) throw new Error(data.message);
        if (!data.data.overview) throw new Error('统计数据格式错误');
        return `统计功能正常，总订单数：${data.data.overview.totalOrders}`;
      }
    },
    {
      name: '分页功能测试',
      test: async () => {
        const page1 = await fetch('/api/orders/unified-simple?page=1&limit=2');
        const page2 = await fetch('/api/orders/unified-simple?page=2&limit=2');
        const data1 = await page1.json();
        const data2 = await page2.json();
        
        if (!data1.success || !data2.success) throw new Error('分页请求失败');
        
        return `分页功能正常，第1页：${data1.data.orders.length}条，第2页：${data2.data.orders.length}条`;
      }
    },
    {
      name: '数据格式验证测试',
      test: async () => {
        const response = await fetch('/api/orders/unified-simple?page=1&limit=1');
        const data = await response.json();
        
        if (!data.success) throw new Error(data.message);
        if (data.data.orders.length === 0) return '数据格式验证通过（无数据）';
        
        const order = data.data.orders[0];
        const requiredFields = ['id', 'orderNumber', 'orderType', 'status', 'createdAt'];
        
        for (const field of requiredFields) {
          if (!(field in order)) throw new Error(`缺少必需字段：${field}`);
        }
        
        return '数据格式验证通过，所有必需字段存在';
      }
    },
    {
      name: '错误处理测试',
      test: async () => {
        try {
          const response = await fetch('/api/orders/unified-simple?page=-1&limit=0');
          const data = await response.json();
          // 即使参数不合理，API也应该能正常处理
          return '错误处理正常，API能处理异常参数';
        } catch (error) {
          return '错误处理正常，捕获到网络错误';
        }
      }
    },
    {
      name: '性能测试（大数据量）',
      test: async () => {
        const startTime = Date.now();
        const response = await fetch('/api/orders/unified-simple?page=1&limit=50');
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        const data = await response.json();
        if (!data.success) throw new Error(data.message);
        
        if (duration > 5000) {
          throw new Error(`响应时间过长：${duration}ms`);
        }
        
        return `性能测试通过，响应时间：${duration}ms`;
      }
    },
    {
      name: '并发请求测试',
      test: async () => {
        const startTime = Date.now();
        
        // 同时发送5个请求
        const promises = Array.from({ length: 5 }, (_, i) => 
          fetch(`/api/orders/unified-simple?page=${i + 1}&limit=5`)
        );
        
        const responses = await Promise.all(promises);
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // 检查所有请求是否成功
        for (const response of responses) {
          const data = await response.json();
          if (!data.success) throw new Error('并发请求中有失败的请求');
        }
        
        return `并发测试通过，5个请求总耗时：${duration}ms`;
      }
    }
  ];

  // 运行单个测试
  const runSingleTest = async (testCase: typeof testCases[0], index: number) => {
    const startTime = Date.now();
    
    setTestResults(prev => prev.map((result, i) => 
      i === index ? { ...result, status: 'running' } : result
    ));

    try {
      const message = await testCase.test();
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map((result, i) => 
        i === index ? { 
          ...result, 
          status: 'success', 
          message,
          duration 
        } : result
      ));
    } catch (error) {
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map((result, i) => 
        i === index ? { 
          ...result, 
          status: 'error', 
          message: error instanceof Error ? error.message : '未知错误',
          duration 
        } : result
      ));
    }
  };

  // 运行所有测试
  const runAllTests = async () => {
    setIsRunning(true);
    setOverallStatus('running');
    
    // 初始化测试结果
    setTestResults(testCases.map(testCase => ({
      name: testCase.name,
      status: 'pending',
      message: '等待执行...'
    })));

    // 逐个运行测试
    for (let i = 0; i < testCases.length; i++) {
      await runSingleTest(testCases[i], i);
      // 在测试之间添加小延迟，避免过于频繁的请求
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunning(false);
    setOverallStatus('completed');
  };

  // 获取状态图标
  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return (
          <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
      case 'running':
        return (
          <svg className="w-5 h-5 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      case 'success':
        return (
          <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  // 获取状态样式
  const getStatusStyle = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
    }
  };

  // 计算测试统计
  const getTestStats = () => {
    const total = testResults.length;
    const success = testResults.filter(r => r.status === 'success').length;
    const error = testResults.filter(r => r.status === 'error').length;
    const pending = testResults.filter(r => r.status === 'pending').length;
    const running = testResults.filter(r => r.status === 'running').length;
    
    return { total, success, error, pending, running };
  };

  const stats = getTestStats();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/orders"
                  className="text-blue-600 hover:text-blue-800"
                >
                  ← 返回订单管理
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-blue-700">订单管理功能测试</h1>
                  <p className="mt-1 text-sm text-gray-600">
                    全面测试订单管理系统的各项功能
                  </p>
                </div>
              </div>
              <div>
                <button
                  onClick={runAllTests}
                  disabled={isRunning}
                  className={`px-6 py-2 rounded-md font-medium transition-colors ${
                    isRunning
                      ? 'bg-gray-400 text-white cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isRunning ? '测试进行中...' : '开始测试'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 测试统计 */}
        {testResults.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-blue-700">测试统计</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">总测试数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.success}</div>
                  <div className="text-sm text-gray-600">成功</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{stats.error}</div>
                  <div className="text-sm text-gray-600">失败</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.running}</div>
                  <div className="text-sm text-gray-600">运行中</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
                  <div className="text-sm text-gray-600">等待中</div>
                </div>
              </div>
              
              {overallStatus === 'completed' && (
                <div className="mt-4 p-4 rounded-md bg-blue-50 border border-blue-200">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    <div className="text-blue-700">
                      <div className="font-medium">测试完成</div>
                      <div className="text-sm">
                        成功率：{stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0}%
                        {stats.error > 0 && ` (${stats.error} 个测试失败)`}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 测试结果列表 */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-blue-700">测试结果</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {testResults.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                点击"开始测试"按钮开始功能测试
              </div>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 mt-1">
                      {getStatusIcon(result.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-gray-900">{result.name}</h3>
                        <div className="flex items-center space-x-2">
                          {result.duration && (
                            <span className="text-xs text-gray-500">{result.duration}ms</span>
                          )}
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusStyle(result.status)}`}>
                            {result.status === 'pending' ? '等待' :
                             result.status === 'running' ? '运行中' :
                             result.status === 'success' ? '成功' : '失败'}
                          </span>
                        </div>
                      </div>
                      <p className="mt-1 text-sm text-gray-600">{result.message}</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 测试说明 */}
        <div className="mt-6 bg-white rounded-lg shadow-sm border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-blue-700">测试说明</h2>
          </div>
          <div className="p-6">
            <div className="prose prose-sm max-w-none">
              <h3 className="text-base font-medium text-gray-900 mb-3">测试覆盖范围</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• <strong>API功能测试</strong>：验证所有订单相关API的基本功能</li>
                <li>• <strong>筛选功能测试</strong>：测试订单类型、日期范围等筛选功能</li>
                <li>• <strong>搜索功能测试</strong>：验证关键词搜索和精确匹配功能</li>
                <li>• <strong>分页功能测试</strong>：确保分页逻辑正确工作</li>
                <li>• <strong>数据格式验证</strong>：检查API返回数据的完整性和格式</li>
                <li>• <strong>错误处理测试</strong>：验证异常情况下的错误处理</li>
                <li>• <strong>性能测试</strong>：检查响应时间和大数据量处理能力</li>
                <li>• <strong>并发测试</strong>：验证系统在并发请求下的稳定性</li>
              </ul>
              
              <h3 className="text-base font-medium text-gray-900 mb-3 mt-6">性能指标</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• <strong>响应时间</strong>：单个API请求应在5秒内完成</li>
                <li>• <strong>并发处理</strong>：系统应能同时处理多个请求</li>
                <li>• <strong>数据完整性</strong>：所有必需字段应正确返回</li>
                <li>• <strong>错误恢复</strong>：异常情况下应有适当的错误提示</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
