'use client';

import React, { useState, useEffect } from 'react';
import { Supplier, SupplierSearchParams, COOPERATION_STATUS_OPTIONS, STATUS_OPTIONS } from '@/types/supplier';
import SupplierForm from './components/SupplierForm';
import ErrorModal from '@/app/components/ErrorModal';
import SuccessModal from '@/app/components/SuccessModal';

export default function SuppliersPage() {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchParams, setSearchParams] = useState<SupplierSearchParams>({
    keyword: '',
    cooperation_status: 'all',
    status: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  
  // 模态框状态
  const [showSupplierForm, setShowSupplierForm] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // 获取供应商列表
  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/suppliers');
      const result = await response.json();
      
      if (result.success) {
        setSuppliers(result.data || []);
      } else {
        setErrorMessage(result.message || '获取供应商列表失败');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      setErrorMessage('获取供应商列表失败');
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  // 筛选供应商
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesKeyword = !searchParams.keyword || 
      supplier.name.toLowerCase().includes(searchParams.keyword.toLowerCase()) ||
      supplier.contact_person?.toLowerCase().includes(searchParams.keyword.toLowerCase()) ||
      supplier.phone?.includes(searchParams.keyword) ||
      supplier.email?.toLowerCase().includes(searchParams.keyword.toLowerCase());
    
    const matchesCooperationStatus = searchParams.cooperation_status === 'all' || 
      supplier.cooperation_status === searchParams.cooperation_status;
    
    const matchesStatus = searchParams.status === 'all' || 
      supplier.status === searchParams.status;
    
    return matchesKeyword && matchesCooperationStatus && matchesStatus;
  });

  // 排序供应商
  const sortedSuppliers = [...filteredSuppliers].sort((a, b) => {
    const field = searchParams.sortBy || 'created_at';
    const order = searchParams.sortOrder === 'desc' ? -1 : 1;
    
    if (field === 'name') {
      return a.name.localeCompare(b.name) * order;
    } else if (field === 'created_at' || field === 'updated_at') {
      return (new Date(a[field]).getTime() - new Date(b[field]).getTime()) * order;
    }
    return 0;
  });

  // 处理添加供应商
  const handleAddSupplier = () => {
    setEditingSupplier(null);
    setShowSupplierForm(true);
  };

  // 处理编辑供应商
  const handleEditSupplier = (supplier: Supplier) => {
    setEditingSupplier(supplier);
    setShowSupplierForm(true);
  };

  // 处理删除供应商
  const handleDeleteSupplier = async (supplier: Supplier) => {
    if (!confirm(`确定要删除供应商 "${supplier.name}" 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const response = await fetch(`/api/suppliers?id=${supplier.id}`, {
        method: 'DELETE'
      });
      const result = await response.json();
      
      if (result.success) {
        setSuccessMessage(result.message || '供应商删除成功');
        setShowSuccessModal(true);
        fetchSuppliers(); // 重新获取列表
      } else {
        setErrorMessage(result.message || '删除供应商失败');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('删除供应商失败:', error);
      setErrorMessage('删除供应商失败');
      setShowErrorModal(true);
    }
  };

  // 处理表单提交成功
  const handleFormSuccess = (message: string) => {
    setShowSupplierForm(false);
    setEditingSupplier(null);
    setSuccessMessage(message);
    setShowSuccessModal(true);
    fetchSuppliers(); // 重新获取列表
  };

  // 处理表单错误
  const handleFormError = (message: string) => {
    setErrorMessage(message);
    setShowErrorModal(true);
  };

  // 获取合作状态显示
  const getCooperationStatusDisplay = (status: string) => {
    const option = COOPERATION_STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.label : status;
  };

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    const option = STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.label : status;
  };

  // 获取状态颜色
  const getStatusColor = (status: string, type: 'cooperation' | 'status') => {
    const options = type === 'cooperation' ? COOPERATION_STATUS_OPTIONS : STATUS_OPTIONS;
    const option = options.find(opt => opt.value === status);
    return option ? option.color : 'gray';
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-blue-700 mb-2">供应商管理</h1>
        <p className="text-gray-600">管理药店供应商信息，维护供应商档案</p>
      </div>

      {/* 搜索和筛选区域 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">
              搜索关键词
            </label>
            <input
              type="text"
              placeholder="搜索供应商名称、联系人、电话..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
              value={searchParams.keyword || ''}
              onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">
              合作状态
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
              value={searchParams.cooperation_status || 'all'}
              onChange={(e) => setSearchParams(prev => ({ ...prev, cooperation_status: e.target.value as any }))}
            >
              <option value="all">全部状态</option>
              {COOPERATION_STATUS_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">
              启用状态
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
              value={searchParams.status || 'all'}
              onChange={(e) => setSearchParams(prev => ({ ...prev, status: e.target.value as any }))}
            >
              <option value="all">全部状态</option>
              {STATUS_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={handleAddSupplier}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              添加供应商
            </button>
          </div>
        </div>
      </div>

      {/* 供应商列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold text-blue-700">
              供应商列表 ({sortedSuppliers.length})
            </h2>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">排序：</span>
              <select
                className="text-sm border border-gray-300 rounded px-2 py-1 text-blue-700"
                value={`${searchParams.sortBy}-${searchParams.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('-');
                  setSearchParams(prev => ({ ...prev, sortBy: sortBy as any, sortOrder: sortOrder as any }));
                }}
              >
                <option value="created_at-desc">创建时间 (新到旧)</option>
                <option value="created_at-asc">创建时间 (旧到新)</option>
                <option value="name-asc">名称 (A-Z)</option>
                <option value="name-desc">名称 (Z-A)</option>
                <option value="updated_at-desc">更新时间 (新到旧)</option>
              </select>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="text-blue-700">加载中...</div>
          </div>
        ) : sortedSuppliers.length === 0 ? (
          <div className="p-8 text-center">
            <div className="text-gray-500">
              {suppliers.length === 0 ? '暂无供应商数据' : '没有符合条件的供应商'}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    供应商信息
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    联系方式
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    证照信息
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedSuppliers.map((supplier) => (
                  <tr key={supplier.id} className="hover:bg-gray-50">
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-blue-700">
                          {supplier.name}
                        </div>
                        {supplier.contact_person && (
                          <div className="text-sm text-gray-600">
                            联系人：{supplier.contact_person}
                          </div>
                        )}
                        {supplier.business_scope && (
                          <div className="text-xs text-gray-500 mt-1">
                            {supplier.business_scope.length > 30 
                              ? `${supplier.business_scope.substring(0, 30)}...` 
                              : supplier.business_scope}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm text-blue-700">
                        {supplier.phone && <div>电话：{supplier.phone}</div>}
                        {supplier.email && <div>邮箱：{supplier.email}</div>}
                        {supplier.address && (
                          <div className="text-xs text-gray-500 mt-1">
                            地址：{supplier.address.length > 20 
                              ? `${supplier.address.substring(0, 20)}...` 
                              : supplier.address}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm text-blue-700">
                        {supplier.license_number && (
                          <div>执照：{supplier.license_number}</div>
                        )}
                        {supplier.gsp_certificate && (
                          <div>GSP：{supplier.gsp_certificate}</div>
                        )}
                        {supplier.tax_number && (
                          <div className="text-xs text-gray-500">
                            税号：{supplier.tax_number}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          getStatusColor(supplier.cooperation_status, 'cooperation') === 'green' 
                            ? 'bg-green-100 text-green-800'
                            : getStatusColor(supplier.cooperation_status, 'cooperation') === 'yellow'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {getCooperationStatusDisplay(supplier.cooperation_status)}
                        </span>
                        <br />
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          supplier.status === 'active' 
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {getStatusDisplay(supplier.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditSupplier(supplier)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          编辑
                        </button>
                        <button
                          onClick={() => handleDeleteSupplier(supplier)}
                          className="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 供应商表单模态框 */}
      <SupplierForm
        isOpen={showSupplierForm}
        supplier={editingSupplier}
        onSuccess={handleFormSuccess}
        onError={handleFormError}
        onClose={() => {
          setShowSupplierForm(false);
          setEditingSupplier(null);
        }}
      />

      {/* 错误提示模态框 */}
      <ErrorModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        message={errorMessage}
      />

      {/* 成功提示模态框 */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        message={successMessage}
      />
    </div>
  );
}
