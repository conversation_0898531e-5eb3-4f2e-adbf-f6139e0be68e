import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 操作日志API
 * GET /api/audit-logs - 查询操作日志
 * POST /api/audit-logs - 记录操作日志
 */

// 查询操作日志
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    const action = searchParams.get('action') || '';
    const userId = searchParams.get('userId') || '';

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereConditions: string[] = [];
    let queryParams: any[] = [];

    if (startDate) {
      whereConditions.push('操作时间 >= ?');
      queryParams.push(startDate + ' 00:00:00');
    }

    if (endDate) {
      whereConditions.push('操作时间 <= ?');
      queryParams.push(endDate + ' 23:59:59');
    }

    if (action) {
      whereConditions.push('操作类型 LIKE ?');
      queryParams.push(`%${action}%`);
    }

    if (userId) {
      whereConditions.push('用户编号 = ?');
      queryParams.push(userId);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // 查询日志记录
    const logs = await query(`
      SELECT 
        编号,
        用户编号,
        用户名,
        操作类型,
        操作对象,
        操作详情,
        IP地址,
        用户代理,
        操作时间,
        操作结果
      FROM 操作日志
      ${whereClause}
      ORDER BY 操作时间 DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    // 获取总数
    const countResult = await query(`
      SELECT COUNT(*) as total FROM 操作日志 ${whereClause}
    `, queryParams);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        logs: logs.map((log: any) => ({
          id: log.编号,
          userId: log.用户编号,
          userName: log.用户名,
          action: log.操作类型,
          target: log.操作对象,
          details: log.操作详情,
          ipAddress: log.IP地址,
          userAgent: log.用户代理,
          timestamp: log.操作时间,
          result: log.操作结果
        })),
        total,
        page,
        limit,
        totalPages
      }
    });

  } catch (error) {
    console.error('查询操作日志失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '查询操作日志失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 记录操作日志
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId = 1,
      userName = '系统管理员',
      action,
      target,
      details,
      result = 'success'
    } = body;

    // 获取客户端信息
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     '127.0.0.1';
    const userAgent = request.headers.get('user-agent') || '';

    // 插入日志记录
    await query(`
      INSERT INTO 操作日志 (
        用户编号, 用户名, 操作类型, 操作对象, 操作详情, 
        IP地址, 用户代理, 操作时间, 操作结果
      ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?)
    `, [userId, userName, action, target, details, ipAddress, userAgent, result]);

    return NextResponse.json({
      success: true,
      message: '操作日志记录成功'
    });

  } catch (error) {
    console.error('记录操作日志失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '记录操作日志失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 辅助函数：记录操作日志
export async function logOperation(
  action: string,
  target: string,
  details: string,
  userId: number = 1,
  userName: string = '系统管理员',
  result: string = 'success'
) {
  try {
    await query(`
      INSERT INTO 操作日志 (
        用户编号, 用户名, 操作类型, 操作对象, 操作详情, 
        IP地址, 用户代理, 操作时间, 操作结果
      ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?)
    `, [userId, userName, action, target, details, '127.0.0.1', 'System', result]);
  } catch (error) {
    console.error('记录操作日志失败:', error);
  }
}
