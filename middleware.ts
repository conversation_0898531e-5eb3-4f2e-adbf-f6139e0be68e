import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 这个中间件在Edge Runtime中运行，不能使用Node.js特定的API
export async function middleware(request: NextRequest) {
  // 简单地继续处理请求，不进行数据库初始化
  return NextResponse.next();
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径，但不包括:
     * - API 路由
     * - 静态文件路由
     * - 图片文件
     * - 字体文件
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:jpg|jpeg|gif|png|svg|webp)).*)',
  ],
};
