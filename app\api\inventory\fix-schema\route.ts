import { NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * POST 请求处理函数 - 修复库存记录表结构
 */
export async function POST() {
  try {
    console.log('开始修复库存记录表结构...');

    // 1. 检查当前表结构
    const currentStructure = await query("PRAGMA table_info(库存记录)");
    console.log('当前表结构:', currentStructure);

    // 2. 备份现有数据
    const existingData = await query('SELECT * FROM 库存记录');
    console.log(`备份现有数据，共 ${existingData.length} 条记录`);

    // 3. 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 4. 重命名原表
      await run('ALTER TABLE 库存记录 RENAME TO 库存记录_备份');
      console.log('原表已重命名为 库存记录_备份');

      // 5. 创建新的表结构
      await run(`
        CREATE TABLE 库存记录 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          药品编号 INTEGER NOT NULL,
          操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '盘点', '调整')),
          数量变化 INTEGER NOT NULL,
          操作前库存 INTEGER NOT NULL DEFAULT 0,
          操作后库存 INTEGER NOT NULL DEFAULT 0,
          供应商编号 INTEGER,
          批次号 TEXT,
          有效期 DATE,
          成本价 DECIMAL(10,2),
          操作人 TEXT,
          操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          备注 TEXT,
          码上放心单据号 TEXT,
          码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')),
          码上放心上传时间 DATETIME,
          码上放心响应 TEXT,
          FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
          FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
        )
      `);
      console.log('新表结构创建成功');

      // 6. 迁移数据
      if (existingData.length > 0) {
        for (const record of existingData) {
          // 映射字段名称并设置默认值
          await run(`
            INSERT INTO 库存记录 (
              药品编号, 操作类型, 数量变化, 操作前库存, 操作后库存,
              供应商编号, 批次号, 有效期, 成本价, 操作人, 操作时间, 备注,
              码上放心单据号, 码上放心上传状态, 码上放心上传时间, 码上放心响应
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            record.药品编号,
            record.变动类型 || '调整', // 变动类型 → 操作类型
            record.变动数量 || 0,     // 变动数量 → 数量变化
            0,                       // 操作前库存（新字段，设为0）
            0,                       // 操作后库存（新字段，设为0）
            record.供应商编号,
            record.批次号,
            record.有效期,
            record.成本价,
            '系统迁移',               // 操作人（新字段）
            record.创建时间 || new Date().toISOString(), // 创建时间 → 操作时间
            record.备注,
            record.码上放心单据号,
            record.码上放心上传状态,
            record.码上放心上传时间,
            record.码上放心响应
          ]);
        }
        console.log(`成功迁移 ${existingData.length} 条记录`);
      }

      // 7. 提交事务
      await run('COMMIT');
      console.log('事务提交成功');

      // 8. 验证新表结构
      const newStructure = await query("PRAGMA table_info(库存记录)");
      console.log('新表结构:', newStructure);

      // 9. 验证数据迁移
      const migratedData = await query('SELECT COUNT(*) as count FROM 库存记录');
      console.log(`迁移后数据量: ${migratedData[0].count}`);

      return NextResponse.json({
        success: true,
        message: '库存记录表结构修复成功',
        data: {
          original_records: existingData.length,
          migrated_records: migratedData[0].count,
          new_structure: newStructure
        }
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      console.error('修复过程中出错，已回滚:', error);
      throw error;
    }

  } catch (error) {
    console.error('修复库存记录表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '修复库存记录表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * GET 请求处理函数 - 检查修复状态
 */
export async function GET() {
  try {
    // 检查新表结构
    const structure = await query("PRAGMA table_info(库存记录)");
    const hasOperationType = structure.some((col: any) => col.name === '操作类型');
    const hasQuantityChange = structure.some((col: any) => col.name === '数量变化');
    const hasPreStock = structure.some((col: any) => col.name === '操作前库存');
    const hasPostStock = structure.some((col: any) => col.name === '操作后库存');
    const hasOperator = structure.some((col: any) => col.name === '操作人');

    // 检查备份表是否存在
    const backupExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='库存记录_备份'"
    );

    return NextResponse.json({
      success: true,
      data: {
        table_structure: structure,
        required_fields_check: {
          has_operation_type: hasOperationType,
          has_quantity_change: hasQuantityChange,
          has_pre_stock: hasPreStock,
          has_post_stock: hasPostStock,
          has_operator: hasOperator
        },
        backup_table_exists: backupExists.length > 0,
        is_fixed: hasOperationType && hasQuantityChange && hasPreStock && hasPostStock
      }
    });
  } catch (error) {
    console.error('检查修复状态失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '检查修复状态失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
