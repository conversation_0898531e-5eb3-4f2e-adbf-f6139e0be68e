import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const productId = searchParams.get('product_id');
    
    if (!productId) {
      return NextResponse.json({ 
        success: false, 
        message: '缺少必要参数' 
      }, { status: 400 });
    }

    const batches = await query(`
      SELECT b.*, s.name as supplier_name
      FROM batch b
      LEFT JOIN supplier s ON b.supplier_id = s.id
      WHERE b.product_id = ?
      ORDER BY b.expiry_date ASC
    `, [productId]);

    return NextResponse.json({ 
      success: true, 
      data: batches
    });
  } catch (error) {
    console.error('获取批次数据错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理批次数据时出错' 
    }, { status: 500 });
  }
} 