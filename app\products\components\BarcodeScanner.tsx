'use client';

import { useState, useEffect, useRef } from 'react';

interface BarcodeScannerProps {
  onScan: (code: string) => void;
  isActive: boolean;
}

/**
 * 条码扫描组件
 * 监听键盘输入，捕获扫码枪输入的条码数据
 */
export default function BarcodeScanner({ onScan, isActive }: BarcodeScannerProps) {
  const [barcode, setBarcode] = useState<string>('');
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 处理键盘输入
  const handleKeyDown = (e: KeyboardEvent) => {
    // 如果组件不处于激活状态，不处理键盘输入
    if (!isActive) return;
    
    // 如果按下的是Enter键，表示扫码结束
    if (e.key === 'Enter') {
      if (barcode.length > 0) {
        onScan(barcode);
        setBarcode('');
      }
      setIsScanning(false);
      return;
    }
    
    // 如果是数字或字母，添加到条码中
    if (/^[a-zA-Z0-9]$/.test(e.key)) {
      setIsScanning(true);
      setBarcode(prev => prev + e.key);
      
      // 重置超时计时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // 设置新的超时计时器，如果500ms内没有新的输入，认为扫码结束
      timeoutRef.current = setTimeout(() => {
        if (barcode.length > 0) {
          onScan(barcode + e.key);
          setBarcode('');
        }
        setIsScanning(false);
      }, 500);
    }
  };
  
  // 组件挂载时添加键盘事件监听
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    
    // 组件卸载时移除键盘事件监听
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [barcode, isActive]);
  
  return (
    <div className="relative">
      {isScanning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">正在扫描条码...</h3>
            <div className="flex items-center justify-center">
              <div className="animate-pulse text-blue-700 text-xl font-bold">{barcode}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
