'use client';

import { useState, useEffect } from 'react';

export default function DbMigratePage() {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const runMigration = async () => {
    try {
      setStatus('loading');
      setMessage('正在执行数据库迁移...');

      const response = await fetch('/api/db-migrate');
      const data = await response.json();

      if (data.success) {
        setStatus('success');
        setMessage('数据库迁移成功完成');
      } else {
        setStatus('error');
        setMessage(`数据库迁移失败: ${data.message || '未知错误'}`);
      }
    } catch (error) {
      setStatus('error');
      setMessage(`数据库迁移失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">数据库迁移工具</h1>
      
      <div className="mb-6">
        <p className="mb-4">此工具将执行数据库迁移，添加药品追溯码字段到药品表。</p>
        <button
          onClick={runMigration}
          disabled={status === 'loading'}
          className={`px-4 py-2 rounded-md ${
            status === 'loading'
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {status === 'loading' ? '执行中...' : '执行数据库迁移'}
        </button>
      </div>

      {status !== 'idle' && (
        <div
          className={`p-4 rounded-md ${
            status === 'loading'
              ? 'bg-blue-100 text-blue-800'
              : status === 'success'
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}
        >
          <p>{message}</p>
          {status === 'success' && (
            <div className="mt-4">
              <p>迁移成功完成，现在您可以：</p>
              <ul className="list-disc ml-6 mt-2">
                <li>
                  <a href="/products" className="text-blue-600 hover:underline">
                    返回药品管理页面
                  </a>
                </li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
