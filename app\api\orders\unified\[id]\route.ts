import { NextRequest, NextResponse } from 'next/server';
import { query, get } from '@/lib/db';

/**
 * 统一订单详情查询API
 * GET /api/orders/unified/[id]?type=sales|stock_in|stock_out|inventory|adjustment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const searchParams = request.nextUrl.searchParams;
    const orderType = searchParams.get('type');
    const orderId = resolvedParams.id;

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: '订单ID不能为空' },
        { status: 400 }
      );
    }

    let orderDetail: any = null;

    if (orderType === 'sales') {
      // 查询销售订单详情
      const salesOrder = await get(
        `SELECT 
          so.*,
          c.名称 as customer_name,
          c.phone as customer_phone,
          c.address as customer_address,
          c.is_member as customer_is_member,
          c.member_number as customer_member_number
         FROM 销售订单 so
         LEFT JOIN 客户信息 c ON so.客户编号 = c.编号
         WHERE so.编号 = ?`,
        [orderId]
      );

      if (!salesOrder) {
        return NextResponse.json(
          { success: false, message: '销售订单不存在' },
          { status: 404 }
        );
      }

      // 查询订单明细
      const orderItems = await query(
        `SELECT 
          od.*,
          p.名称 as product_name,
          p.规格 as specification,
          p.条形码 as barcode,
          p.生产厂家 as manufacturer,
          p.剂型 as dosage_form
         FROM 订单明细 od
         JOIN 药品信息 p ON od.product_id = p.编号
         WHERE od.order_id = ?
         ORDER BY od.id`,
        [orderId]
      );

      orderDetail = {
        id: salesOrder.编号,
        orderNumber: salesOrder.订单编号,
        orderType: 'sales',
        status: salesOrder.状态,
        createdAt: salesOrder.创建时间,
        
        // 金额信息
        totalAmount: parseFloat(salesOrder.总金额 || '0'),
        discountAmount: parseFloat(salesOrder.discount_amount || '0'),
        payableAmount: parseFloat(salesOrder.payable_amount || '0'),
        receivedAmount: parseFloat(salesOrder.received_amount || '0'),
        changeAmount: parseFloat(salesOrder.change_amount || '0'),
        paymentMethod: salesOrder.payment_method,
        
        // 客户信息
        customer: salesOrder.customer_name ? {
          id: salesOrder.客户编号,
          name: salesOrder.customer_name,
          phone: salesOrder.customer_phone || '',
          address: salesOrder.customer_address || '',
          isMember: salesOrder.customer_is_member === 1,
          memberNumber: salesOrder.customer_member_number || ''
        } : null,
        
        // 操作信息
        operatorId: salesOrder.operator_id,
        note: salesOrder.note || '',
        
        // 订单明细
        items: orderItems.map((item: any) => ({
          id: item.id,
          productId: item.product_id,
          productName: item.product_name,
          specification: item.specification || '',
          barcode: item.barcode || '',
          manufacturer: item.manufacturer || '',
          dosageForm: item.dosage_form || '',
          quantity: parseInt(item.quantity),
          unitPrice: parseFloat(item.unit_price || '0'),
          subtotal: parseFloat(item.subtotal || '0'),
          discount: parseFloat(item.discount || '0')
        }))
      };

    } else {
      // 查询库存记录详情
      const inventoryRecord = await get(
        `SELECT 
          lr.*,
          p.名称 as product_name,
          p.规格 as specification,
          p.条形码 as barcode,
          p.生产厂家 as manufacturer,
          p.剂型 as dosage_form,
          s.名称 as supplier_name,
          s.联系人 as supplier_contact,
          s.电话 as supplier_phone,
          s.地址 as supplier_address
         FROM 库存记录 lr
         JOIN 药品信息 p ON lr.药品编号 = p.编号
         LEFT JOIN 供应商 s ON lr.供应商编号 = s.编号
         WHERE lr.编号 = ?`,
        [orderId]
      );

      if (!inventoryRecord) {
        return NextResponse.json(
          { success: false, message: '库存记录不存在' },
          { status: 404 }
        );
      }

      // 查询追溯码信息
      const traceCodes = await query(
        `SELECT * FROM 药品追溯码记录 WHERE 库存记录编号 = ? ORDER BY 编号`,
        [orderId]
      );

      // 确定订单类型
      let recordOrderType = 'stock_in';
      switch (inventoryRecord.操作类型) {
        case '入库':
          recordOrderType = 'stock_in';
          break;
        case '出库':
          recordOrderType = 'stock_out';
          break;
        case '盘点':
          recordOrderType = 'inventory';
          break;
        case '调整':
          recordOrderType = 'adjustment';
          break;
      }

      orderDetail = {
        id: inventoryRecord.编号,
        orderNumber: inventoryRecord.码上放心单据号 || `SYS-${inventoryRecord.编号}`,
        orderType: recordOrderType,
        status: '已完成',
        createdAt: inventoryRecord.操作时间,
        operationDate: inventoryRecord.操作时间.split(' ')[0],
        
        // 库存操作信息
        operationType: inventoryRecord.操作类型,
        quantityChange: inventoryRecord.数量变化,
        stockBefore: inventoryRecord.操作前库存,
        stockAfter: inventoryRecord.操作后库存,
        
        // 药品信息
        product: {
          id: inventoryRecord.药品编号,
          name: inventoryRecord.product_name,
          specification: inventoryRecord.specification || '',
          barcode: inventoryRecord.barcode || '',
          manufacturer: inventoryRecord.manufacturer || '',
          dosageForm: inventoryRecord.dosage_form || ''
        },
        
        // 批次信息
        batchNumber: inventoryRecord.批次号,
        expiryDate: inventoryRecord.有效期,
        costPrice: parseFloat(inventoryRecord.成本价 || '0'),
        
        // 供应商信息
        supplier: inventoryRecord.supplier_name ? {
          id: inventoryRecord.供应商编号,
          name: inventoryRecord.supplier_name,
          contact: inventoryRecord.supplier_contact || '',
          phone: inventoryRecord.supplier_phone || '',
          address: inventoryRecord.supplier_address || ''
        } : null,
        
        // 操作信息
        operator: inventoryRecord.操作人,
        note: inventoryRecord.备注 || '',
        
        // 码上放心信息
        mashangfangxin: {
          billNumber: inventoryRecord.码上放心单据号,
          uploadStatus: inventoryRecord.码上放心上传状态,
          uploadTime: inventoryRecord.码上放心上传时间,
          response: inventoryRecord.码上放心响应
        },
        
        // 追溯码信息
        traceCodes: traceCodes.map((trace: any) => ({
          id: trace.编号,
          traceCode: trace.追溯码,
          operationType: trace.操作类型,
          createdAt: trace.创建时间
        }))
      };
    }

    return NextResponse.json({
      success: true,
      data: orderDetail
    });

  } catch (error) {
    console.error('查询统一订单详情失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '查询订单详情失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
