import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 订单搜索API
 * GET /api/orders/search?keyword=xxx&type=all&page=1&limit=20
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const keyword = searchParams.get('keyword') || '';
    const searchType = searchParams.get('type') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (!keyword.trim()) {
      return NextResponse.json({
        success: true,
        data: {
          orders: [],
          total: 0,
          page,
          limit,
          totalPages: 0
        }
      });
    }

    const offset = (page - 1) * limit;
    const searchKeyword = `%${keyword.trim()}%`;

    let searchQueries: string[] = [];
    let searchParams: any[] = [];

    // 根据搜索类型构建查询
    switch (searchType) {
      case 'orderNumber':
        // 搜索订单编号
        searchQueries = [
          `SELECT 'sales' as order_type, CAST(编号 AS TEXT) as id, 订单编号 as order_number, 状态 as status, 创建时间 as created_at, 总金额 as total_amount, 备注 as note
           FROM 销售订单 WHERE 订单编号 LIKE ?`,
          `SELECT
             CASE 操作类型
               WHEN '入库' THEN 'stock_in'
               WHEN '出库' THEN 'stock_out'
               WHEN '盘点' THEN 'inventory'
               WHEN '调整' THEN 'adjustment'
             END as order_type,
             CAST(编号 AS TEXT) as id,
             COALESCE(码上放心单据号, 'SYS-' || 编号) as order_number,
             '已完成' as status,
             操作时间 as created_at,
             NULL as total_amount,
             备注 as note
           FROM 库存记录 WHERE COALESCE(码上放心单据号, 'SYS-' || 编号) LIKE ?`
        ];
        searchParams = [searchKeyword, searchKeyword];
        break;

      case 'productName':
        // 搜索药品名称
        searchQueries = [
          `SELECT DISTINCT 'sales' as order_type, CAST(so.编号 AS TEXT) as id, so.订单编号 as order_number, so.状态 as status, so.创建时间 as created_at, so.总金额 as total_amount, so.备注 as note
           FROM 销售订单 so
           JOIN 订单明细 od ON so.编号 = od.order_id
           JOIN 药品信息 p ON od.product_id = p.编号
           WHERE p.名称 LIKE ?`,
          `SELECT DISTINCT
             CASE lr.操作类型
               WHEN '入库' THEN 'stock_in'
               WHEN '出库' THEN 'stock_out'
               WHEN '盘点' THEN 'inventory'
               WHEN '调整' THEN 'adjustment'
             END as order_type,
             CAST(lr.编号 AS TEXT) as id,
             COALESCE(lr.码上放心单据号, 'SYS-' || lr.编号) as order_number,
             '已完成' as status,
             lr.操作时间 as created_at,
             NULL as total_amount,
             lr.备注 as note
           FROM 库存记录 lr
           JOIN 药品信息 p ON lr.药品编号 = p.编号
           WHERE p.名称 LIKE ?`
        ];
        searchParams = [searchKeyword, searchKeyword];
        break;

      case 'barcode':
        // 搜索条形码
        searchQueries = [
          `SELECT DISTINCT 'sales' as order_type, CAST(so.id AS TEXT) as id, so.order_number, so.status, so.created_at, so.total_amount, so.note
           FROM 销售订单 so
           JOIN 订单明细 od ON so.id = od.order_id
           JOIN 药品信息 p ON od.product_id = p.编号
           WHERE p.条形码 LIKE ?`,
          `SELECT DISTINCT
             CASE lr.操作类型
               WHEN '入库' THEN 'stock_in'
               WHEN '出库' THEN 'stock_out'
               WHEN '盘点' THEN 'inventory'
               WHEN '调整' THEN 'adjustment'
             END as order_type,
             CAST(lr.编号 AS TEXT) as id,
             COALESCE(lr.码上放心单据号, 'SYS-' || lr.编号) as order_number,
             '已完成' as status,
             lr.操作时间 as created_at,
             NULL as total_amount,
             lr.备注 as note
           FROM 库存记录 lr
           JOIN 药品信息 p ON lr.药品编号 = p.编号
           WHERE p.条形码 LIKE ?`
        ];
        searchParams = [searchKeyword, searchKeyword];
        break;

      case 'batchNumber':
        // 搜索批次号
        searchQueries = [
          `SELECT 
             CASE 操作类型
               WHEN '入库' THEN 'stock_in'
               WHEN '出库' THEN 'stock_out'
               WHEN '盘点' THEN 'inventory'
               WHEN '调整' THEN 'adjustment'
             END as order_type,
             CAST(编号 AS TEXT) as id,
             COALESCE(码上放心单据号, 'SYS-' || 编号) as order_number,
             '已完成' as status,
             操作时间 as created_at,
             NULL as total_amount,
             备注 as note
           FROM 库存记录 WHERE 批次号 LIKE ?`
        ];
        searchParams = [searchKeyword];
        break;

      case 'customer':
        // 搜索客户信息
        searchQueries = [
          `SELECT DISTINCT 'sales' as order_type, CAST(so.id AS TEXT) as id, so.order_number, so.status, so.created_at, so.total_amount, so.note
           FROM 销售订单 so
           LEFT JOIN customers c ON so.customer_id = c.id
           WHERE c.name LIKE ? OR c.phone LIKE ?`
        ];
        searchParams = [searchKeyword, searchKeyword];
        break;

      case 'supplier':
        // 搜索供应商信息
        searchQueries = [
          `SELECT DISTINCT
             CASE lr.操作类型
               WHEN '入库' THEN 'stock_in'
               WHEN '出库' THEN 'stock_out'
               WHEN '盘点' THEN 'inventory'
               WHEN '调整' THEN 'adjustment'
             END as order_type,
             CAST(lr.编号 AS TEXT) as id,
             COALESCE(lr.码上放心单据号, 'SYS-' || lr.编号) as order_number,
             '已完成' as status,
             lr.操作时间 as created_at,
             NULL as total_amount,
             lr.备注 as note
           FROM 库存记录 lr
           LEFT JOIN 供应商信息表 s ON lr.供应商编号 = s.编号
           WHERE s.名称 LIKE ?`
        ];
        searchParams = [searchKeyword];
        break;

      default:
        // 全文搜索
        searchQueries = [
          // 销售订单搜索
          `SELECT DISTINCT 'sales' as order_type, CAST(so.id AS TEXT) as id, so.order_number, so.status, so.created_at, so.total_amount, so.note
           FROM 销售订单 so
           LEFT JOIN 订单明细 od ON so.id = od.order_id
           LEFT JOIN 药品信息 p ON od.product_id = p.编号
           LEFT JOIN customers c ON so.customer_id = c.id
           WHERE so.order_number LIKE ? OR so.note LIKE ? OR p.名称 LIKE ? OR p.条形码 LIKE ? OR c.name LIKE ? OR c.phone LIKE ?`,
          
          // 库存记录搜索
          `SELECT DISTINCT
             CASE lr.操作类型
               WHEN '入库' THEN 'stock_in'
               WHEN '出库' THEN 'stock_out'
               WHEN '盘点' THEN 'inventory'
               WHEN '调整' THEN 'adjustment'
             END as order_type,
             CAST(lr.编号 AS TEXT) as id,
             COALESCE(lr.码上放心单据号, 'SYS-' || lr.编号) as order_number,
             '已完成' as status,
             lr.操作时间 as created_at,
             NULL as total_amount,
             lr.备注 as note
           FROM 库存记录 lr
           LEFT JOIN 药品信息 p ON lr.药品编号 = p.编号
           LEFT JOIN 供应商信息表 s ON lr.供应商编号 = s.编号
           WHERE COALESCE(lr.码上放心单据号, 'SYS-' || lr.编号) LIKE ? OR lr.备注 LIKE ? OR lr.批次号 LIKE ? OR p.名称 LIKE ? OR p.条形码 LIKE ? OR s.名称 LIKE ?`
        ];
        searchParams = [
          searchKeyword, searchKeyword, searchKeyword, searchKeyword, searchKeyword, searchKeyword, // 销售订单
          searchKeyword, searchKeyword, searchKeyword, searchKeyword, searchKeyword, searchKeyword  // 库存记录
        ];
        break;
    }

    // 合并查询
    const unionQuery = searchQueries.join(' UNION ALL ');
    const finalQuery = `${unionQuery} ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`;

    // 执行搜索
    const results = await query(finalQuery, searchParams);

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM (${unionQuery}) as search_results`;
    const countResult = await query(countQuery, searchParams);
    const total = countResult[0]?.total || 0;

    // 格式化结果
    const formattedResults = results.map((result: any) => ({
      id: result.id,
      orderNumber: result.order_number,
      orderType: result.order_type,
      status: result.status,
      createdAt: result.created_at,
      totalAmount: result.total_amount ? parseFloat(result.total_amount) : null,
      note: result.note || ''
    }));

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        orders: formattedResults,
        total,
        page,
        limit,
        totalPages,
        keyword,
        searchType
      }
    });

  } catch (error) {
    console.error('订单搜索失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '搜索失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
