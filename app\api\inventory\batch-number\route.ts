import { NextRequest, NextResponse } from 'next/server';
import { query, get } from '@/lib/db';

/**
 * 获取下一个批次号
 * GET /api/inventory/batch-number?type=PC&product_id=1
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const batchType = searchParams.get('type') || 'PC'; // 默认为采购批次 (Purchase)
    const productId = searchParams.get('product_id');

    // 获取系统设置中的订单编号位数（批次号使用相同的位数设置）
    const settingsResult = await get(
      'SELECT setting_value FROM settings WHERE setting_name = ?',
      ['orderNumberDigits']
    );
    const batchNumberDigits = parseInt(settingsResult?.setting_value || '4');

    // 生成今日日期字符串
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // 查询今日该类型批次的最大序号
    const todayPrefix = `${batchType}-${dateStr}`;
    
    // 从库存记录表中查询批次号（因为批次号存储在库存记录中）
    const maxBatchResult = await get(`
      SELECT 批次号 
      FROM 库存记录 
      WHERE 批次号 LIKE ? 
        AND 批次号 IS NOT NULL 
        AND 批次号 != ''
      ORDER BY 批次号 DESC 
      LIMIT 1
    `, [`${todayPrefix}%`]);

    let nextSequence = 1;
    
    if (maxBatchResult && maxBatchResult.批次号) {
      // 从最大批次号中提取序号
      const batchNumber = maxBatchResult.批次号;
      const sequencePart = batchNumber.substring(todayPrefix.length);
      const currentSequence = parseInt(sequencePart);
      
      if (!isNaN(currentSequence)) {
        nextSequence = currentSequence + 1;
      }
    }

    // 生成新的批次号
    const sequenceNumber = String(nextSequence).padStart(batchNumberDigits, '0');
    const newBatchNumber = `${todayPrefix}${sequenceNumber}`;

    // 如果提供了产品ID，获取产品信息
    let productInfo = null;
    if (productId) {
      const productResult = await get(
        'SELECT 编号, 名称, 规格 FROM 药品信息 WHERE 编号 = ?',
        [productId]
      );
      productInfo = productResult;
    }

    return NextResponse.json({
      success: true,
      data: {
        batchNumber: newBatchNumber,
        sequence: nextSequence,
        dateStr: dateStr,
        batchType: batchType,
        productInfo: productInfo,
        suggestion: {
          description: getBatchTypeDescription(batchType),
          format: `${batchType}-YYYYMMDD${sequenceNumber}`,
          example: newBatchNumber
        }
      }
    });
  } catch (error) {
    console.error('生成批次号失败:', error);
    return NextResponse.json(
      { success: false, message: '生成批次号失败' },
      { status: 500 }
    );
  }
}

/**
 * 验证批次号格式
 * POST /api/inventory/batch-number
 */
export async function POST(request: NextRequest) {
  try {
    const { batchNumber, productId } = await request.json();

    if (!batchNumber) {
      return NextResponse.json(
        { success: false, message: '批次号不能为空' },
        { status: 400 }
      );
    }

    // 基本格式验证
    const validation = validateBatchNumberFormat(batchNumber);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, message: validation.message },
        { status: 400 }
      );
    }

    // 检查批次号是否已存在（如果提供了产品ID）
    let isDuplicate = false;
    if (productId) {
      const existingBatch = await get(
        'SELECT 编号 FROM 库存记录 WHERE 批次号 = ? AND 药品编号 = ?',
        [batchNumber, productId]
      );
      isDuplicate = !!existingBatch;
    }

    return NextResponse.json({
      success: true,
      data: {
        batchNumber: batchNumber,
        isValid: validation.isValid,
        isDuplicate: isDuplicate,
        format: validation.format,
        type: validation.type,
        date: validation.date,
        sequence: validation.sequence
      }
    });
  } catch (error) {
    console.error('验证批次号失败:', error);
    return NextResponse.json(
      { success: false, message: '验证批次号失败' },
      { status: 500 }
    );
  }
}

/**
 * 获取批次类型描述
 */
function getBatchTypeDescription(batchType: string): string {
  const typeMap: Record<string, string> = {
    'PC': '采购批次 (Purchase)',
    'PR': '生产批次 (Production)',
    'RT': '退货批次 (Return)',
    'AD': '调整批次 (Adjustment)',
    'TR': '调拨批次 (Transfer)',
    'QC': '质检批次 (Quality Control)'
  };
  
  return typeMap[batchType] || '未知类型';
}

/**
 * 验证批次号格式
 */
function validateBatchNumberFormat(batchNumber: string): {
  isValid: boolean;
  message?: string;
  format?: string;
  type?: string;
  date?: string;
  sequence?: string;
} {
  if (!batchNumber || batchNumber.trim().length === 0) {
    return { isValid: false, message: '批次号不能为空' };
  }

  const trimmedBatchNumber = batchNumber.trim();

  if (trimmedBatchNumber.length > 50) {
    return { isValid: false, message: '批次号长度不能超过50个字符' };
  }

  // 检查是否符合系统生成的格式：TYPE-YYYYMMDDNNNN
  const systemFormatRegex = /^([A-Z]{2})-(\d{8})(\d+)$/;
  const match = trimmedBatchNumber.match(systemFormatRegex);

  if (match) {
    const [, type, dateStr, sequence] = match;
    
    // 验证日期格式
    const year = parseInt(dateStr.substring(0, 4));
    const month = parseInt(dateStr.substring(4, 6));
    const day = parseInt(dateStr.substring(6, 8));
    
    if (year < 2020 || year > 2050 || month < 1 || month > 12 || day < 1 || day > 31) {
      return { isValid: false, message: '批次号中的日期格式不正确' };
    }

    return {
      isValid: true,
      format: 'system',
      type: type,
      date: dateStr,
      sequence: sequence
    };
  }

  // 如果不是系统格式，检查是否为有效的自定义格式
  if (!/^[A-Za-z0-9\-_\u4e00-\u9fa5]+$/.test(trimmedBatchNumber)) {
    return { isValid: false, message: '批次号只能包含字母、数字、连字符、下划线和中文字符' };
  }

  // 检查是否包含危险字符
  const dangerousChars = ['<', '>', '"', "'", '&', 'script', 'SELECT', 'INSERT', 'UPDATE', 'DELETE'];
  const upperBatchNumber = trimmedBatchNumber.toUpperCase();
  for (const char of dangerousChars) {
    if (upperBatchNumber.includes(char.toUpperCase())) {
      return { isValid: false, message: '批次号包含不允许的字符' };
    }
  }

  return {
    isValid: true,
    format: 'custom'
  };
}
