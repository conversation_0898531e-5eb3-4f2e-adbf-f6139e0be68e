import { NextRequest, NextResponse } from 'next/server';
import { query, get, run } from '@/lib/db';

// 获取单个订单详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const orderId = resolvedParams.id;

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: '订单ID不能为空' },
        { status: 400 }
      );
    }

    // 获取订单基本信息
    const order = await get(`
      SELECT
        o.编号 as id,
        o.订单编号 as orderNumber,
        o.总金额 as total,
        o.discount_amount as discount,
        o.payable_amount as payableAmount,
        o.received_amount as receivedAmount,
        o.change_amount as changeAmount,
        o.payment_method as paymentMethod,
        o.状态 as status,
        o.note as note,
        o.创建时间 as createdAt,
        c.名称 as customerName,
        c.phone as customerPhone,
        c.is_member as customerIsMember,
        c.member_number as customerMemberNumber
      FROM 销售订单 o
      LEFT JOIN 客户信息 c ON o.客户编号 = c.编号
      WHERE o.编号 = ?
    `, [orderId]);

    if (!order) {
      return NextResponse.json(
        { success: false, message: '订单不存在' },
        { status: 404 }
      );
    }

    // 获取订单明细
    const orderItems = await query(`
      SELECT
        od.id as detail_id,
        od.product_id as id,
        od.quantity as quantity,
        od.unit_price as price,
        od.subtotal as subtotal,
        p.名称 as name,
        p.规格 as specification
      FROM 订单明细 od
      JOIN 药品信息 p ON od.product_id = p.编号
      WHERE od.order_id = ?
      ORDER BY od.id ASC
    `, [orderId]);

    // 格式化订单数据
    const orderDetail = {
      id: order.id.toString(),
      orderNumber: order.orderNumber,
      date: order.createdAt.split(' ')[0], // 只取日期部分
      status: order.status,
      customer: order.customerName ? {
        name: order.customerName,
        phone: order.customerPhone || '',
        isMember: order.customerIsMember === 1,
        memberNumber: order.customerMemberNumber || ''
      } : null,
      items: orderItems.map((item: any) => ({
        id: item.id.toString(),
        name: item.name,
        specification: item.specification || '',
        price: parseFloat(item.price),
        quantity: parseInt(item.quantity),
        subtotal: parseFloat(item.subtotal)
      })),
      payment: {
        method: order.paymentMethod,
        amount: parseFloat(order.receivedAmount || order.payableAmount),
        time: order.createdAt
      },
      note: order.note || '',
      total: parseFloat(order.total),
      discount: parseFloat(order.discount || 0),
      staff: '系统管理员', // 暂时固定，后续可以从用户表获取
      createdAt: order.createdAt
    };

    return NextResponse.json({
      success: true,
      data: orderDetail
    });
  } catch (error) {
    console.error('获取订单详情失败:', error);
    return NextResponse.json(
      { success: false, message: '获取订单详情失败' },
      { status: 500 }
    );
  }
}

// 修改订单
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const orderId = resolvedParams.id;
    const data = await request.json();

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: '订单ID不能为空' },
        { status: 400 }
      );
    }

    // 验证订单是否存在
    const existingOrder = await get(
      'SELECT id, status FROM 销售订单 WHERE 编号 = ?',
      [orderId]
    );

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, message: '订单不存在' },
        { status: 404 }
      );
    }

    // 检查订单状态是否允许修改
    if (existingOrder.status === 'cancelled' || existingOrder.status === 'refunded') {
      return NextResponse.json(
        { success: false, message: '已取消或已退款的订单不能修改' },
        { status: 400 }
      );
    }

    const {
      customerName,
      customerPhone,
      isMember,
      memberNumber,
      note,
      discountAmount,
      items
    } = data;

    // 添加详细的调试日志
    console.log('API接收到的修改数据:', {
      customerName,
      customerPhone,
      isMember,
      memberNumber,
      note,
      discountAmount,
      itemsCount: items?.length || 0,
      items: items
    });

    // 验证必要参数
    if (!items || !Array.isArray(items)) {
      console.error('items参数无效:', items);
      return NextResponse.json(
        { success: false, message: '订单项目数据格式错误' },
        { status: 400 }
      );
    }

    if (items.length === 0) {
      console.error('items数组为空');
      return NextResponse.json(
        { success: false, message: '订单必须包含至少一个药品项目' },
        { status: 400 }
      );
    }

    // 验证每个订单项目
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      console.log(`验证第${i + 1}个项目:`, item);

      if (!item.id || !item.name || item.price === undefined || item.quantity === undefined) {
        console.error(`第${i + 1}个项目数据不完整:`, item);
        return NextResponse.json(
          { success: false, message: `第${i + 1}个药品项目数据不完整` },
          { status: 400 }
        );
      }

      // 验证数值类型
      if (isNaN(parseFloat(item.price)) || isNaN(parseInt(item.quantity))) {
        console.error(`第${i + 1}个项目数值格式错误:`, item);
        return NextResponse.json(
          { success: false, message: `第${i + 1}个药品项目的价格或数量格式错误` },
          { status: 400 }
        );
      }
    }

    // 开始事务
    console.log('开始数据库事务');
    await run('BEGIN TRANSACTION');

    try {
      // 1. 更新或创建客户信息
      let customerId = null;
      console.log('处理客户信息:', { customerName, customerPhone, isMember, memberNumber });

      if (customerName && customerName !== '散客') {
        // 检查客户是否已存在
        const existingCustomer = await get(
          'SELECT id FROM 客户信息 WHERE name = ? AND phone = ?',
          [customerName, customerPhone || '']
        );

        if (existingCustomer) {
          customerId = existingCustomer.id;
          console.log('更新现有客户:', customerId);
          // 更新客户信息
          await run(
            `UPDATE 客户信息 SET
             is_member = ?, member_number = ?
             WHERE 编号 = ?`,
            [isMember ? 1 : 0, memberNumber || '', customerId]
          );
        } else {
          console.log('创建新客户');
          // 创建新客户
          const customerResult = await run(
            `INSERT INTO 客户信息 (name, phone, is_member, member_number)
             VALUES (?, ?, ?, ?)`,
            [customerName, customerPhone || '', isMember ? 1 : 0, memberNumber || '']
          );
          customerId = customerResult.lastID;
          console.log('新客户ID:', customerId);
        }
      }

      // 2. 计算新的金额
      const totalAmount = items.reduce((sum: number, item: any) => sum + (parseFloat(item.price) * parseInt(item.quantity)), 0);
      const payableAmount = totalAmount - (parseFloat(discountAmount) || 0);

      console.log('计算金额:', { totalAmount, discountAmount, payableAmount });

      // 3. 更新订单基本信息
      console.log('更新订单基本信息');
      await run(
        `UPDATE 销售订单 SET
         customer_id = ?, total_amount = ?, discount_amount = ?,
         payable_amount = ?, note = ?
         WHERE 编号 = ?`,
        [customerId, totalAmount, discountAmount || 0, payableAmount, note || '', orderId]
      );

      // 4. 删除原有订单明细
      console.log('删除原有订单明细');
      const deleteResult = await run('DELETE FROM 订单明细 WHERE order_id = ?', [orderId]);
      console.log('删除的明细数量:', deleteResult.changes);

      // 5. 插入新的订单明细
      console.log('插入新的订单明细，数量:', items.length);
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const subtotal = parseFloat(item.price) * parseInt(item.quantity);

        console.log(`插入第${i + 1}个明细:`, {
          orderId,
          productId: item.id,
          quantity: item.quantity,
          unitPrice: item.price,
          subtotal
        });

        await run(
          `INSERT INTO 订单明细 (order_id, product_id, quantity, unit_price, subtotal)
           VALUES (?, ?, ?, ?, ?)`,
          [
            orderId,
            item.id,
            parseInt(item.quantity),
            parseFloat(item.price),
            subtotal
          ]
        );
      }

      // 提交事务
      console.log('提交事务');
      await run('COMMIT');

      console.log('订单修改成功');
      return NextResponse.json({
        success: true,
        message: '订单修改成功'
      });

    } catch (error) {
      // 回滚事务
      console.error('事务执行失败，回滚:', error);
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('修改订单失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '修改订单失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
