{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020", "DOM"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "allowJs": true, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}