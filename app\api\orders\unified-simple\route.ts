import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 简化版统一订单查询API
 * GET /api/orders/unified-simple
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 筛选参数
    const orderTypes = searchParams.get('orderType')?.split(',') || [];
    const status = searchParams.get('status')?.split(',') || [];
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    const quickFilter = searchParams.get('quickFilter') || ''; // today, week, month, quarter, year

    // 构建日期筛选条件
    let dateFilter = '';
    let dateParams: string[] = [];

    if (quickFilter) {
      const now = new Date();
      let startDateTime = '';
      let endDateTime = '';

      switch (quickFilter) {
        case 'today':
          startDateTime = now.toISOString().split('T')[0] + ' 00:00:00';
          endDateTime = now.toISOString().split('T')[0] + ' 23:59:59';
          break;
        case 'week':
          const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
          startDateTime = weekStart.toISOString().split('T')[0] + ' 00:00:00';
          endDateTime = new Date().toISOString().split('T')[0] + ' 23:59:59';
          break;
        case 'month':
          startDateTime = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0] + ' 00:00:00';
          endDateTime = new Date().toISOString().split('T')[0] + ' 23:59:59';
          break;
      }

      if (startDateTime && endDateTime) {
        dateFilter = 'AND 创建时间 BETWEEN ? AND ?';
        dateParams = [startDateTime, endDateTime];
      }
    } else if (startDate || endDate) {
      if (startDate && endDate) {
        dateFilter = 'AND 创建时间 BETWEEN ? AND ?';
        dateParams = [startDate + ' 00:00:00', endDate + ' 23:59:59'];
      } else if (startDate) {
        dateFilter = 'AND 创建时间 >= ?';
        dateParams = [startDate + ' 00:00:00'];
      } else if (endDate) {
        dateFilter = 'AND 创建时间 <= ?';
        dateParams = [endDate + ' 23:59:59'];
      }
    }

    // 构建状态筛选条件
    let statusFilter = '';
    let statusParams: string[] = [];
    if (status.length > 0) {
      statusFilter = `AND 状态 IN (${status.map(() => '?').join(', ')})`;
      statusParams = status;
    }

    // 查询销售订单
    let salesOrders: any[] = [];
    if (orderTypes.length === 0 || orderTypes.includes('sales')) {
      salesOrders = await query(`
        SELECT
          'sales' as order_type,
          CAST(编号 AS TEXT) as id,
          订单编号 as order_number,
          状态 as status,
          总金额 as total_amount,
          创建时间 as created_at,
          备注 as note
        FROM 销售订单
        WHERE 1=1 ${dateFilter} ${statusFilter}
        ORDER BY 创建时间 DESC
        LIMIT ? OFFSET ?
      `, [...dateParams, ...statusParams, limit, offset]);
    }

    // 查询库存记录
    let inventoryRecords: any[] = [];
    const inventoryTypes = orderTypes.filter(type =>
      ['stock_in', 'stock_out', 'inventory', 'adjustment'].includes(type)
    );

    if (orderTypes.length === 0 || inventoryTypes.length > 0) {
      // 构建操作类型筛选
      let operationTypeFilter = '';
      let operationTypeParams: string[] = [];

      if (inventoryTypes.length > 0) {
        const operationTypes = inventoryTypes.map(type => {
          switch (type) {
            case 'stock_in': return '入库';
            case 'stock_out': return '出库';
            case 'inventory': return '盘点';
            case 'adjustment': return '调整';
            default: return '';
          }
        }).filter(Boolean);

        if (operationTypes.length > 0) {
          operationTypeFilter = `AND 操作类型 IN (${operationTypes.map(() => '?').join(', ')})`;
          operationTypeParams = operationTypes;
        }
      }

      // 库存记录的日期筛选使用操作时间字段
      const inventoryDateFilter = dateFilter.replace(/创建时间/g, '操作时间');

      inventoryRecords = await query(`
        SELECT
          CASE 操作类型
            WHEN '入库' THEN 'stock_in'
            WHEN '出库' THEN 'stock_out'
            WHEN '盘点' THEN 'inventory'
            WHEN '调整' THEN 'adjustment'
            ELSE 'unknown'
          END as order_type,
          CAST(编号 AS TEXT) as id,
          COALESCE(码上放心单据号, 'SYS-' || 编号) as order_number,
          '已完成' as status,
          NULL as total_amount,
          操作时间 as created_at,
          备注 as note
        FROM 库存记录
        WHERE 1=1 ${inventoryDateFilter} ${operationTypeFilter}
        ORDER BY 操作时间 DESC
        LIMIT ? OFFSET ?
      `, [...dateParams, ...operationTypeParams, limit, offset]);
    }

    // 合并结果并按时间排序
    const allOrders = [...salesOrders, ...inventoryRecords]
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, limit);

    // 获取总数
    const salesCount = await query('SELECT COUNT(*) as count FROM 销售订单');
    const inventoryCount = await query('SELECT COUNT(*) as count FROM 库存记录');
    const total = (salesCount[0]?.count || 0) + (inventoryCount[0]?.count || 0);

    // 格式化订单数据
    const formattedOrders = allOrders.map((order: any) => ({
      id: order.id,
      orderNumber: order.order_number,
      orderType: order.order_type,
      status: order.status,
      createdAt: order.created_at,
      operationDate: order.created_at.split(' ')[0],
      totalAmount: order.total_amount ? parseFloat(order.total_amount) : null,
      note: order.note || '',
      operator: '系统操作员',
      itemsCount: 1
    }));

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        orders: formattedOrders,
        total,
        page,
        limit,
        totalPages
      }
    });

  } catch (error) {
    console.error('简化版统一订单查询失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '查询订单失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
