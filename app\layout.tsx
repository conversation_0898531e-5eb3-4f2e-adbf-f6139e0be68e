'use client';

import { Geist, <PERSON>eist_Mono } from "next/font/google";
import { useEffect, useState } from 'react';
import "./globals.css";
import DbInitializer from './db-initializer';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

function Header() {
  const [storeName, setStoreName] = useState('药店零售管理系统');

  const loadSettings = () => {
    if (typeof window !== 'undefined') {
      try {
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          if (parsedSettings.storeName) {
            setStoreName(parsedSettings.storeName);
          }
        }
      } catch (e) {
        console.error('加载设置失败:', e);
      }
    }
  };

  useEffect(() => {
    // 初始加载设置
    loadSettings();

    // 监听设置更新事件
    const handleSettingsUpdate = () => {
      loadSettings();
    };

    window.addEventListener('settingsUpdated', handleSettingsUpdate);

    return () => {
      window.removeEventListener('settingsUpdated', handleSettingsUpdate);
    };
  }, []);

  return (
    <header className="flex items-center justify-between h-16 px-6 bg-gradient-to-r from-blue-600 to-blue-400 text-white shadow-md">
      <a href="/" className="font-bold text-xl tracking-wide hover:text-white/90 transition-colors duration-200 cursor-pointer flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
        {storeName}
      </a>
      <nav className="flex gap-6">
        <a className="hover:underline" href="/orders">订单管理</a>
        <a className="hover:underline" href="/inventory">库存管理</a>
        <a className="hover:underline" href="/reports">报表分析</a>
        <a className="hover:underline" href="/users">用户管理</a>
      </nav>
      <div className="flex items-center gap-4">
        <span className="flex items-center gap-2">
          <img src="/file.svg" alt="avatar" className="w-8 h-8 rounded-full bg-white/30" />
          <span className="font-medium">管理员</span>
        </span>
        <a href="/settings" title="设置" className="hover:bg-white/20 p-2 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </a>
        <button title="退出" className="hover:bg-white/20 p-2 rounded-full">
          <img src="/globe.svg" alt="退出" className="w-5 h-5" />
        </button>
      </div>
    </header>
  );
}

function Sidebar() {
  return (
    <aside className="w-56 bg-gray-50 border-r border-gray-200 h-full flex flex-col py-6 px-2 gap-2 text-gray-800">
      <div className="mb-4 font-semibold text-gray-600 px-2">功能模块</div>
      <a href="/sales" className="flex items-center gap-2 px-3 py-2 rounded hover:bg-blue-100 font-medium text-left">销售管理</a>
      <a href="/products" className="flex items-center gap-2 px-3 py-2 rounded hover:bg-blue-100 font-medium text-left">药品管理</a>
      <a href="/suppliers" className="flex items-center gap-2 px-3 py-2 rounded hover:bg-blue-100 font-medium text-left">供应商管理</a>
      <a href="/reports" className="flex items-center gap-2 px-3 py-2 rounded hover:bg-blue-100 font-medium text-left">数据报表</a>
      <a href="/customers" className="flex items-center gap-2 px-3 py-2 rounded hover:bg-blue-100 font-medium text-left">客户管理</a>
      <a href="/settings" className="flex items-center gap-2 px-3 py-2 rounded hover:bg-blue-100 font-medium text-left">系统设置</a>
      <div className="mt-auto px-2">
        <input className="w-full px-2 py-1 border rounded text-sm" placeholder="全局搜索..." />
      </div>
    </aside>
  );
}

function Footer() {
  const [storeName, setStoreName] = useState('药店零售管理系统');

  const loadSettings = () => {
    if (typeof window !== 'undefined') {
      try {
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          if (parsedSettings.storeName) {
            setStoreName(parsedSettings.storeName);
          }
        }
      } catch (e) {
        console.error('加载设置失败:', e);
      }
    }
  };

  useEffect(() => {
    // 初始加载设置
    loadSettings();

    // 监听设置更新事件
    const handleSettingsUpdate = () => {
      loadSettings();
    };

    window.addEventListener('settingsUpdated', handleSettingsUpdate);

    return () => {
      window.removeEventListener('settingsUpdated', handleSettingsUpdate);
    };
  }, []);

  return (
    <footer className="h-12 flex items-center justify-center text-xs text-gray-500 bg-gray-50 border-t border-gray-200">
      © {new Date().getFullYear()} {storeName}
    </footer>
  );
}

function NotificationBar() {
  // 预留消息通知区域
  return (
    <div className="fixed top-16 right-6 z-50 w-80 max-w-full">
      {/* 示例通知，可后续用状态管理动态渲染 */}
      {/* <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-3 mb-2 rounded shadow">
        操作成功！
      </div> */}
    </div>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-100 min-h-screen flex flex-col`}>
        {/* 数据库初始化组件 */}
        <DbInitializer />
        <Header />
        <NotificationBar />
        <div className="flex flex-1 min-h-0 bg-white">
          <Sidebar />
          <main className="flex-1 p-6 overflow-auto">{children}</main>
        </div>
        <Footer />
      </body>
    </html>
  );
}
