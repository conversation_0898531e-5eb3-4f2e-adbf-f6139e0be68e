# 药店零售管理系统

这是一个基于 Next.js 开发的现代化药店零售管理系统，专为中小型药店设计，提供完整的药品管理、库存控制、销售管理和系统设置功能。

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装和运行

1. 克隆项目到本地：
```bash
git clone <repository-url>
cd retail_trade_system
```

2. 安装依赖：
```bash
npm install
# 或
yarn install
```

3. 启动开发服务器：
```bash
npm run dev
# 或
yarn dev
```

4. 打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 生产环境部署

```bash
# 构建项目
npm run build

# 启动生产服务器
npm run start
```

## 📁 项目结构

```
retail_trade_system/
├── app/                    # Next.js App Router 应用目录
│   ├── api/               # API 路由
│   ├── components/        # 共享组件
│   ├── inventory/         # 库存管理页面
│   ├── products/          # 药品管理页面
│   ├── sales/             # 销售管理页面
│   ├── settings/          # 系统设置页面
│   ├── layout.tsx         # 根布局组件
│   └── page.tsx           # 首页
├── db/                    # 数据库相关文件
│   └── 药店管理系统.db    # SQLite 数据库文件
├── lib/                   # 工具库和配置
│   ├── db.ts             # 数据库连接和操作
│   └── db-init.ts        # 数据库初始化
├── mashangfangxin/        # 码上放心平台集成
├── public/                # 静态资源
├── types/                 # TypeScript 类型定义
├── package.json           # 项目配置和依赖
└── README.md             # 项目文档
```

## 🎯 核心功能

### 1. 药品管理
- ✅ 药品信息的增删改查
- ✅ 药品分类管理
- ✅ 处方药和医保药品标识
- ✅ 扫码枪支持，自动识别药品追溯码
- ✅ 药品批次管理
- ✅ 储存条件设置

### 2. 库存管理
- ✅ 药品入库、出库记录
- ✅ 库存实时查询
- ✅ 库存预警功能
- ✅ 批次追踪管理

### 3. 销售管理
- ✅ 销售订单创建和管理
- ✅ 订单详情查看和编辑
- ✅ 客户信息管理
- ✅ 会员管理功能
- ✅ 销售统计和报表

### 4. 系统设置
- ✅ 药店基本信息设置
- ✅ 码上放心平台配置
- ✅ 订单编号规则设置
- ✅ 数据库管理功能

## 🛠 技术栈

### 前端技术
- **Next.js 15.3.1** - React 全栈框架
- **React 19.0** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS 4** - 原子化 CSS 框架
- **Headless UI** - 无样式 UI 组件库

### 后端技术
- **Next.js API Routes** - 服务端 API
- **SQLite3** - 轻量级关系型数据库
- **better-sqlite3** - 高性能 SQLite 驱动

### 开发工具
- **ESLint** - 代码质量检查
- **PostCSS** - CSS 处理工具

## 💾 数据库设计

系统使用 SQLite 数据库，采用中文表名和字段名设计，主要数据表包括：

### 核心业务表
- **药品信息** - 存储药品基本信息
- **药品分类** - 药品分类管理
- **库存记录** - 库存变动记录
- **销售订单** - 销售订单信息
- **订单明细** - 订单商品明细

### 系统管理表
- **用户** - 系统用户管理
- **系统设置** - 系统配置信息
- **供应商** - 供应商信息管理

数据库文件位置：`db/药店管理系统.db`

## 🔌 码上放心平台集成

系统集成了码上放心开放平台的药品追溯码功能：

### 功能特性
- 🔍 扫码枪扫描药品追溯码
- 🏷️ 自动识别条码类型（药品追溯码 vs 商品条码）
- 📋 自动获取药品详细信息
- ⚡ 快速添加药品到系统

### 配置步骤
1. 在码上放心开放平台注册账号并创建应用
2. 获取 AppKey 和 AppSecret
3. 在系统设置页面配置相关参数：
   - AppKey：应用密钥
   - AppSecret：应用秘钥
   - 企业ID：ref_ent_id 参数
   - API URL：默认为淘宝开放平台地址

### 支持的条码类型
- 药品追溯码：通过码上放心平台获取详细信息
- 商品条码：通常以 69 开头的标准商品条码

## 🚀 部署指南

### Vercel 部署（推荐）
1. 将代码推送到 GitHub 仓库
2. 在 [Vercel](https://vercel.com) 导入项目
3. 配置环境变量（如需要）
4. 部署完成

### 本地生产环境
```bash
# 构建项目
npm run build

# 启动生产服务器
npm start
```

### Docker 部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🔧 开发指南

### 添加新功能
1. 在 `app/` 目录下创建新的页面或组件
2. 在 `app/api/` 目录下添加相应的 API 路由
3. 更新数据库结构（如需要）
4. 添加相应的 TypeScript 类型定义

### 数据库操作
使用 `lib/db.ts` 中的工具函数进行数据库操作：
```typescript
import { query, get, run } from '@/lib/db';

// 查询多条记录
const products = await query('SELECT * FROM 药品信息');

// 查询单条记录
const product = await get('SELECT * FROM 药品信息 WHERE id = ?', [id]);

// 执行插入/更新/删除
await run('INSERT INTO 药品信息 (名称, 价格) VALUES (?, ?)', [name, price]);
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `db/药店管理系统.db` 文件是否存在
   - 确保应用有读写权限

2. **扫码功能不工作**
   - 检查码上放心平台配置是否正确
   - 确认网络连接正常

3. **页面加载缓慢**
   - 检查数据库文件大小
   - 考虑添加数据库索引

### 数据库管理
系统提供内置的数据库管理功能：
- 数据库备份和恢复
- 表结构初始化
- 数据完整性检查

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**药店零售管理系统** - 让药店管理更简单、更高效！
