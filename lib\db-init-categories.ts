import { query, run } from './db';

/**
 * 检查并初始化药品分类数据
 * @returns {Promise<boolean>} 是否成功初始化
 */
export async function ensureCategoriesData(): Promise<boolean> {
  try {
    // 检查药品分类表中是否有数据
    const categoriesCount = await query(
      'SELECT COUNT(*) as count FROM 药品分类'
    );

    if (!categoriesCount || categoriesCount[0].count === 0) {
      console.log('药品分类表中没有数据，正在初始化...');

      // 定义默认药品分类
      const defaultCategories = [
        { name: '中成药', description: '传统中成药制剂' },
        { name: '化学药品', description: '西药、化学药品' },
        { name: '中药材', description: '中药材、饮片' },
        { name: '生物制品', description: '疫苗、血液制品等' },
        { name: '医疗器械', description: '医疗器械、耗材' },
        { name: '保健品', description: '保健食品、功能性食品' },
        { name: '感冒用药', description: '治疗感冒相关症状' },
        { name: '解热镇痛', description: '缓解疼痛和发热' },
        { name: '抗生素', description: '抗菌药物' },
        { name: '维生素类', description: '补充维生素' },
        { name: '清热解毒', description: '清热解毒类中药' }
      ];

      // 使用事务批量插入数据
      await run('BEGIN TRANSACTION');

      try {
        for (const category of defaultCategories) {
          await run(
            'INSERT INTO 药品分类 (名称, 描述) VALUES (?, ?)',
            [category.name, category.description]
          );
        }

        await run('COMMIT');
        console.log(`成功初始化 ${defaultCategories.length} 条药品分类数据`);
      } catch (error) {
        await run('ROLLBACK');
        console.error('初始化药品分类数据失败，事务已回滚:', error);
        throw error;
      }

      return true;
    }

    console.log(`药品分类表中已有 ${categoriesCount[0].count} 条数据，无需初始化`);
    return true;
  } catch (error) {
    console.error('检查或初始化药品分类数据失败:', error);
    throw error;
  }
}
